#pragma once

#include <JuceHeader.h>

class TestOscillatorProcessor : public juce::AudioProcessor
{
public:
    TestOscillatorProcessor();
    ~TestOscillatorProcessor() override;

    void prepareToPlay (double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock (juce::AudioBuffer<float>&, juce::MidiBuffer&) override;

    juce::AudioProcessorEditor* createEditor() override { return nullptr; }
    bool hasEditor() const override { return false; }

    const juce::String getName() const override { return "TOSC"; }

    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return false; }
    bool isMidiEffect() const override { return false; }
    double getTailLengthSeconds() const override { return 0.0; }

    int getNumPrograms() override { return 1; }
    int getCurrentProgram() override { return 0; }
    void setCurrentProgram(int) override {}
    const juce::String getProgramName(int) override { return {}; }
    void changeProgramName(int, const juce::String&) override {}

    void getStateInformation(juce::MemoryBlock&) override {}
    void setStateInformation(const void*, int) override {}

    bool isBusesLayoutSupported(const BusesLayout& layouts) const override
    {
        // 只支持单声道输出
        return layouts.getMainOutputChannelSet() == juce::AudioChannelSet::mono();
    }

private:
    double currentPhase = 0.0;
    double phaseIncrement = 0.0;
    const double frequency = 440.0; // 固定频率 440Hz

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (TestOscillatorProcessor)
}; 