#include "TestOscillatorProcessor.h"

TestOscillatorProcessor::TestOscillatorProcessor()
    : AudioProcessor(BusesProperties()
          .withOutput("Output", juce::AudioChannelSet::mono()))
{
    DBG("TestOscillatorProcessor: Initializing with mono output");
}

TestOscillatorProcessor::~TestOscillatorProcessor()
{
}

void TestOscillatorProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    // Calculate phase increment
    phaseIncrement = (2.0 * juce::MathConstants<double>::pi * frequency) / sampleRate;
    DBG("TestOscillatorProcessor: Prepared with sample rate: " + juce::String(sampleRate) + 
        "Hz, block size: " + juce::String(samplesPerBlock));
}

void TestOscillatorProcessor::releaseResources()
{
}

void TestOscillatorProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    // Clear input
    buffer.clear();
    
    // Generate sine wave
    for (int ch = 0; ch < buffer.getNumChannels(); ++ch)
    {
        float* channelData = buffer.getWritePointer(ch);
        double localPhase = currentPhase;
        
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            channelData[sample] = (float)(std::sin(localPhase) * 0.8); // Set volume to 0.8
            localPhase += phaseIncrement;
        }
    }
    
    // Update phase, ensure it stays between 0 and 2π
    currentPhase = std::fmod(currentPhase + phaseIncrement * buffer.getNumSamples(), 
                           2.0 * juce::MathConstants<double>::pi);
} 