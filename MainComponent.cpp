﻿#include "MainComponent.h"
#include "OscillatorProcessor.h"
#include "AudioInputProcessor.h"
#include "AudioOutputProcessor.h"
#include "MidiInputProcessor.h"
#include "MidiOutputProcessor.h"
#include "TestMidiOutProcessor.h"
#include "TestOscillatorProcessor.h"
#include "AudioInputProcessorComponent.h"
#include "AudioOutputProcessorComponent.h"
#include "MidiInputProcessorComponent.h"
#include "MidiOutputProcessorComponent.h"
#include "TestOscillatorProcessorComponent.h"
#include "DelayProcessor.h"
#include "DelayProcessorComponent.h"
#include "EnvelopeGeneratorProcessor.h"
#include "EnvelopeGeneratorProcessorComponent.h"
#include "OscillatorProcessorComponent.h"
// #include "AudioAnalyzerProcessor.h"
// #include "AudioAnalyzerComponent.h"

MainComponent::MainComponent()
    : AudioAppComponent(deviceManager)
{
    // 使用AudioSettingsWindow初始化设备管理器
    AudioSettingsWindow::initializeAudioDeviceManager(deviceManager, &midiCollector, this);

    // 添加设备管理器监听器，用于处理设备变更
    deviceManager.addChangeListener(this);

    // Settings button
    addAndMakeVisible(settingsButton);
    settingsButton.setButtonText("Audio Settings");
    settingsButton.onClick = [this]
    {
        settingsButtonClicked();
    };

    // Command input box
    addAndMakeVisible(searchBox);
    searchBox.setTextToShowWhenEmpty("Enter command (e.g.: osc1, c osc1 0 aou1 0)", juce::Colours::grey);
    searchBox.addListener(this);

    // 创建模块管理器
    moduleManager = std::make_unique<ModuleManager>(deviceManager);
    // moduleManager->initialize(); // 由于重构，这个方法已不需要，相关初始化在构造函数中完成

    setSize(800, 600);

    // Start audio
    setAudioChannels(2, 2);

    // 显示消息
    DBG("MainComponent initialized");
}

MainComponent::~MainComponent()
{
    // 设置关闭标志，这样releaseResources方法知道是真正的关闭
    isApplicationClosing = true;

    // 移除设备管理器监听器
    deviceManager.removeChangeListener(this);

    // 1. 关闭音频设备
    shutdownAudio();

    // 2. 清理模块管理器
    if (moduleManager != nullptr)
    {
        // 在应用程序关闭时，明确释放所有资源
        // 注意：shutdownAudio()会调用releaseResources()，所以这里不需要再调用
        // moduleManager->releaseResources();

        // 确保在释放模块管理器前，先释放所有模块UI组件
        if (auto* moduleSystem = moduleManager->getModuleSystem())
        {
            if (auto* audioGraph = moduleSystem->getAudioGraph())
            {
                // 暂停处理
                audioGraph->suspendProcessing(true);

                // 移除所有节点的连接，代替clearConnections
                for (auto node : audioGraph->getNodes())
                {
                    audioGraph->disconnectNode(node->nodeID);
                }

                // 移除所有节点
                for (auto node : audioGraph->getNodes())
                {
                    audioGraph->removeNode(node->nodeID);
                }
            }

            // 移除所有子组件
            moduleSystem->removeAllChildren();
        }

        // 重置模块管理器
        moduleManager.reset();
    }

    // 3. 清理设备管理器
    AudioSettingsWindow::cleanupMidiDevices(deviceManager, &midiCollector, this);

    // 确保所有临时监听器都被移除
    deviceManager.removeAllChangeListeners();

    // 关闭音频设备
    deviceManager.closeAudioDevice();
}

void MainComponent::prepareToPlay(int samplesPerBlockExpected, double sampleRate)
{
    currentSampleRate = sampleRate;
    currentBlockSize = samplesPerBlockExpected;

    // 初始化MidiMessageCollector
    midiCollector.reset(sampleRate);

    // 准备模块管理器
    if (moduleManager != nullptr)
    {
        // 使用updateAudioSettings而不是prepareToPlay，以避免重建模块
        if (moduleManager->getModuleSystem() != nullptr)
        {
            moduleManager->getModuleSystem()->updateAudioSettings(sampleRate, samplesPerBlockExpected);
        }
        else
        {
            // 如果模块系统不存在，则使用prepareToPlay
            moduleManager->prepareToPlay(sampleRate, samplesPerBlockExpected);
        }
    }
}

void MainComponent::getNextAudioBlock(const juce::AudioSourceChannelInfo& bufferToFill)
{
    // 创建临时缓冲区
    juce::AudioBuffer<float> buffer(bufferToFill.buffer->getNumChannels(),
                                  bufferToFill.buffer->getNumSamples());

    // 创建MIDI缓冲区
    juce::MidiBuffer midiBuffer;

    // 从MidiMessageCollector获取MIDI消息
    midiCollector.removeNextBlockOfMessages(midiBuffer, buffer.getNumSamples());

    // 处理模块
    if (moduleManager != nullptr)
    {
        moduleManager->processBlock(buffer, midiBuffer);
    }

    // 检查是否有MIDI信号并打印调试信息
    if (!midiBuffer.isEmpty())
    {
        DBG("Audio block contains MIDI data:");
        for (const auto metadata : midiBuffer)
        {
            auto message = metadata.getMessage();
            if (message.isNoteOn())
                DBG(" - Note On: " + juce::String(message.getNoteNumber()) +
                    " Velocity: " + juce::String(message.getVelocity()) +
                    " Channel: " + juce::String(message.getChannel()));
            else if (message.isNoteOff())
                DBG(" - Note Off: " + juce::String(message.getNoteNumber()) +
                    " Channel: " + juce::String(message.getChannel()));
            else if (message.isController())
                DBG(" - Controller: " + juce::String(message.getControllerNumber()) +
                    " Value: " + juce::String(message.getControllerValue()));
            else
                DBG(" - Other MIDI: " + message.getDescription());
        }
    }

    // 复制音频数据到输出缓冲区
    for (int channel = 0; channel < bufferToFill.buffer->getNumChannels(); ++channel)
    {
        bufferToFill.buffer->copyFrom(channel, bufferToFill.startSample,
                                    buffer, channel, 0, bufferToFill.numSamples);
    }
}

void MainComponent::releaseResources()
{
    // 使用AudioSettingsWindow清理MIDI设备回调
    AudioSettingsWindow::cleanupMidiDevices(deviceManager, &midiCollector, this);

    if (moduleManager != nullptr)
    {
        if (isApplicationClosing)
        {
            // 如果应用程序正在关闭，完全释放资源
            DBG("Application is closing, releasing all resources");
            moduleManager->releaseResources();
        }
        else
        {
            // 如果只是修改buffer size，只暂停处理，不清除模块
            DBG("Audio device changing, suspending processing but preserving modules");
            if (moduleManager->getModuleSystem() != nullptr)
            {
                if (auto* audioGraph = moduleManager->getModuleSystem()->getAudioGraph())
                {
                    audioGraph->suspendProcessing(true);
                }
            }
            
            // 注意：我们在这里没有重新添加MIDI回调，因为这个方法是在设备变更过程中调用的
            // MIDI回调会在changeListenerCallback中重新添加
        }
    }
}

void MainComponent::paint(juce::Graphics& g)
{
    g.fillAll(juce::Colours::black);
}

void MainComponent::resized()
{
    DBG("MainComponent resized: " + juce::String(getWidth()) + "x" + juce::String(getHeight()));

    auto b = getLocalBounds();

    // Keep top area for controls
    int topControlHeight = 70;
    auto topArea = b.removeFromTop(topControlHeight);

    // Set settings button in top left corner
    settingsButton.setBounds(topArea.removeFromLeft(100).reduced(10));

    // Search box occupies remaining top space
    searchBox.setBounds(topArea.reduced(10));

    // Ensure buttons and input box are in front
    settingsButton.toFront(false);
    searchBox.toFront(false);

    // Calculate remaining area for modules
    DBG("Setting up module area: " + juce::String(b.getWidth()) + "x" + juce::String(b.getHeight()));

    // Layout modules in the remaining area
    if (moduleManager != nullptr)
    {
        // 获取 moduleSystemComponent 并设置边界
        auto* moduleSystemComponent = moduleManager->getModuleSystem();
        if (moduleSystemComponent != nullptr)
        {
            // 检查moduleSystemComponent是否已经是子组件
            bool alreadyChild = false;
            for (int i = 0; i < getNumChildComponents(); i++)
            {
                if (getChildComponent(i) == moduleSystemComponent)
                {
                    alreadyChild = true;
                    break;
                }
            }

            // 只有在还不是子组件时才添加
            if (!alreadyChild)
            {
                addAndMakeVisible(moduleSystemComponent);
                DBG("Added ModuleSystemComponent to MainComponent");
            }

            // 设置边界
            moduleSystemComponent->setBounds(b);

            // 将moduleSystem移到后面，这样不会盖住模块UI
            moduleSystemComponent->toBack();

            // 在这里执行布局，而不是依赖ModuleSystemComponent的resized()方法
            moduleManager->layoutModules(moduleSystemComponent);

            DBG("ModuleSystemComponent configured in MainComponent.");
        }
    }
}

void MainComponent::textEditorTextChanged(juce::TextEditor& editor)
{
    juce::ignoreUnused(editor);
    // 不再打印每次的输入变化
}

void MainComponent::textEditorReturnKeyPressed(juce::TextEditor& editor)
{
    // Process command when enter key is pressed
    if (&editor == &searchBox)
    {
        juce::String command = editor.getText();
        if (command.isNotEmpty())
        {
            processCommand(command);
            editor.clear(); // Clear the text field after processing
        }
    }
}

void MainComponent::textEditorEscapeKeyPressed(juce::TextEditor& editor)
{
    // 处理ESC键按下
    if (&editor == &searchBox)
    {
        editor.clear();
    }
}

void MainComponent::textEditorFocusLost(juce::TextEditor& editor)
{
    juce::ignoreUnused(editor);
    // 处理失去焦点 - 不需要打印信息
}

void MainComponent::processCommand(const juce::String& command)
{
    DBG("Processing command: " + command);

    if (moduleManager == nullptr)
        return;

    if (command.startsWith("c "))
    {
        // Connection command - 创建辅助方法处理连接命令
        processConnectionCommand(command);
    }
    else if (command.isEmpty())
    {
        // Do nothing for empty commands
        return;
    }
    else if (command.toLowerCase() == "ag" || command.toLowerCase() == "audiograph")
    {
        // 显示当前音频图状态，但不创建或清除模块
        juce::String state = moduleManager->getModuleState();
        DBG(state);
    }
    else
    {
        // Module creation command
        moduleManager->createModuleFromName(command);

        // After creating a module, make sure to layout the UI components
        if (auto* moduleSystemComponent = moduleManager->getModuleSystem())
        {
            moduleManager->layoutModules(moduleSystemComponent);
        }
    }
}

// 添加一个处理连接命令的辅助方法
void MainComponent::processConnectionCommand(const juce::String& command)
{
    // 连接命令直接传递给ModuleManager处理，ModuleManager内部会调用ModuleConnectionManager
    AudioGraphSnapshot snapshot;
    moduleManager->takeAudioGraphSnapshot(snapshot);

    bool result = false;

    // 使用ModuleSystemComponent处理连接
    if (auto* moduleSystem = moduleManager->getModuleSystem())
    {
        auto parts = juce::StringArray::fromTokens(command, " ", "");

        if (parts.size() >= 5)
        {
            juce::String sourceModule = parts[1];
            int sourceChannel = parts[2].getIntValue();
            juce::String destModule = parts[3];
            int destChannel = parts[4].getIntValue();

            auto sourceNodeId = moduleManager->findNodeIdForName(sourceModule);
            auto destNodeId = moduleManager->findNodeIdForName(destModule);

            if (sourceNodeId.uid != 0 && destNodeId.uid != 0)
            {
                result = moduleSystem->connectProcessors(
                    sourceNodeId, sourceChannel,
                    destNodeId, destChannel);

                if (result)
                    DBG("Connection successful: " + command);
                else
                    DBG("Connection failed: " + command);
            }
        }
    }
}

void MainComponent::handleIncomingMidiMessage(juce::MidiInput* source, const juce::MidiMessage& message)
{
    juce::ignoreUnused(source);

    // Add MIDI message to MidiMessageCollector
    midiCollector.addMessageToQueue(message);
    DBG("MainComponent: Added MIDI message to collector: " + message.getDescription() +
        (message.isNoteOn() ? " [Note On: " + juce::String(message.getNoteNumber()) +
        ", Velocity: " + juce::String(message.getVelocity()) + "]" : "") +
        (message.isNoteOff() ? " [Note Off: " + juce::String(message.getNoteNumber()) + "]" : ""));
}

void MainComponent::changeListenerCallback(juce::ChangeBroadcaster* source)
{
    DBG("MainComponent: Change listener callback triggered");

    if (source == &deviceManager)
    {
        DBG("Audio device settings changed - notifying ModuleManager");

        // We don't need to handle this here anymore
        // Let the ModuleManager handle it with its new methods
        // This avoids conflicting with ModuleManager's handleAudioSettingsChange

        // Just for logging
        auto* device = deviceManager.getCurrentAudioDevice();
        if (device != nullptr)
        {
            double newSampleRate = device->getCurrentSampleRate();
            int newBlockSize = device->getCurrentBufferSizeSamples();

            DBG("Current device settings: SR=" + juce::String(newSampleRate) +
                "Hz, BS=" + juce::String(newBlockSize) + " samples");
            
            // 确保MIDI回调仍然正确设置
            // 首先清理MIDI设备回调
            AudioSettingsWindow::cleanupMidiDevices(deviceManager, &midiCollector, this);
            
            // 重置MidiMessageCollector以更新采样率
            midiCollector.reset(newSampleRate);
            
            // 重新添加MIDI设备回调
            auto midiInputs = juce::MidiInput::getAvailableDevices();
            for (const auto& input : midiInputs)
            {
                if (deviceManager.isMidiInputDeviceEnabled(input.identifier))
                {
                    // 为当前MidiCollector实例添加回调
                    deviceManager.addMidiInputDeviceCallback(input.identifier, &midiCollector);
                    deviceManager.addMidiInputDeviceCallback(input.identifier, this);
                    DBG("Re-added MIDI callbacks for: " + input.name);
                }
            }
        }
    }
}

void MainComponent::settingsButtonClicked()
{
    // 显示音频设置对话框
    AudioSettingsWindow::showAudioSettings(deviceManager, moduleManager.get());
}