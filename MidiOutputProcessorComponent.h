﻿#pragma once

#include <JuceHeader.h>
#include "MidiOutputProcessor.h"

class MidiOutputProcessorComponent : public juce::Component
{
public:
    MidiOutputProcessorComponent(MidiOutputProcessor& p)
        : processor(p)
    {
        setSize(200, 120);
    }

    void paint(juce::Graphics& g) override
    {
        // 绘制背景
        g.fillAll(juce::Colours::darkgrey);
        
        // 绘制边框
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(2), 5.0f, 2.0f);
        
        // 绘制标题
        g.setFont(15.0f);
        g.drawText("MIDI Output", getLocalBounds().removeFromTop(30),
                  juce::Justification::centred, true);
    }

    void resized() override
    {
        // 简化版本不需要额外的控件
    }

    MidiOutputProcessor* getProcessor() const { return &processor; }

private:
    MidiOutputProcessor& processor;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MidiOutputProcessorComponent)
}; 