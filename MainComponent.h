﻿#pragma once

#include <JuceHeader.h>
#include "AudioSettingsWindow.h"
#include "ModuleManager.h"
#include "ProjectManager.h"

class MainComponent   : public juce::AudioAppComponent,
                       public juce::TextEditor::Listener,
                       public juce::ChangeListener,
                       private juce::MidiInputCallback
{
public:
    //==============================================================================
    MainComponent();
    ~MainComponent() override;

    // AudioAppComponent接口
    void prepareToPlay(int samplesPerBlockExpected, double sampleRate) override;
    void getNextAudioBlock(const juce::AudioSourceChannelInfo& bufferToFill) override;
    void releaseResources() override;

    // UI 函数
    void paint(juce::Graphics& g) override;
    void resized() override;

    // 文本输入处理
    void textEditorTextChanged(juce::TextEditor&) override;
    void textEditorReturnKeyPressed(juce::TextEditor&) override;
    void textEditorEscapeKeyPressed(juce::TextEditor&) override;
    void textEditorFocusLost(juce::TextEditor&) override;

    // ChangeListener接口实现
    void changeListenerCallback(juce::ChangeBroadcaster* source) override;

    void processCommand(const juce::String& command);
    void processConnectionCommand(const juce::String& command);

    // 实现MidiInputCallback接口，用于MIDI信号处理
    void handleIncomingMidiMessage(juce::MidiInput* source, const juce::MidiMessage& message) override;

    // 文件操作
    void saveProject();
    void loadProject();
    void handleAsyncSaveResult(int result, const juce::File& file);
    void handleAsyncLoadResult(int result, const juce::File& file);

private:
    // 设置按钮回调
    void settingsButtonClicked();

    // UI组件
    juce::TextButton settingsButton;
    juce::TextButton saveButton;
    juce::TextButton loadButton;
    juce::TextEditor searchBox;

    // 设备管理
    juce::AudioDeviceManager deviceManager;
    juce::MidiMessageCollector midiCollector;

    // 音频处理参数
    double currentSampleRate = 0.0;
    int currentBlockSize = 0;

    // 标记应用程序是否正在关闭
    bool isApplicationClosing = false;

    // 模块管理器
    std::unique_ptr<ModuleManager> moduleManager;

    // 项目管理器
    std::unique_ptr<ProjectManager> projectManager;

    // 当前项目文件
    juce::File currentProjectFile;

    // 保存对话框的共享指针（确保对话框在异步操作期间不被销毁）
    std::shared_ptr<juce::AlertWindow> saveDialogPtr;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MainComponent)
};
