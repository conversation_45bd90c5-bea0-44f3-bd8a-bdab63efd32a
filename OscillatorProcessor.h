﻿#pragma once

#include <JuceHeader.h>

//==============================================================================
class OscillatorSound : public juce::SynthesiserSound
{
public:
    OscillatorSound() {}
    bool appliesToNote(int) override { return true; }
    bool appliesToChannel(int) override { return true; }
};

//==============================================================================
class OscillatorVoice : public juce::SynthesiserVoice
{
public:
    OscillatorVoice() 
    {
        // 初始化DSP波形生成器
        sineOsc.initialise([](float x) { return std::sin(x); });
        sawOsc.initialise([](float x) { return juce::jmap(x, 0.0f, juce::MathConstants<float>::twoPi, -1.0f, 1.0f); });
        // squareOsc.initialise([](float x) { 
        //     return std::fmod(x, juce::MathConstants<float>::twoPi) < juce::MathConstants<float>::pi ? 1.0f : -1.0f; 
        // });
        squareOsc.initialise([](float x) { return x < 0.0f ? -1.0f : 1.0f; });
    }

    bool canPlaySound(juce::SynthesiserSound* sound) override
    {
        return dynamic_cast<OscillatorSound*>(sound) != nullptr;
    }

    void startNote(int midiNoteNumber, float velocity, juce::SynthesiserSound*, int) override
    {
        // 保存当前MIDI音符编号
        currentMidiNote = midiNoteNumber;

        // 计算实际频率，包括八度、半音和微调
        float adjustedNote = static_cast<float>(midiNoteNumber) + 
                           octaveShift * 12.0f + 
                           semitoneShift + 
                           detuneValue / 100.0f; // cents to semitones
                           
        frequency = juce::MidiMessage::getMidiNoteInHertz(adjustedNote);
        // DBG("OscillatorVoice::startNote - Frequency: " + juce::String(frequency) + " Hz");
        level = static_cast<float>(velocity * 0.15);
        
        // 重置所有振荡器并设置频率
        auto sampleRate = getSampleRate();
        sineOsc.setFrequency(frequency, sampleRate);
        sawOsc.setFrequency(frequency, sampleRate);
        squareOsc.setFrequency(frequency, sampleRate);
        
        tailOff = 0.0;
        isPlaying = true;
        
        // 打印调试信息
        DBG("OscillatorVoice::startNote - Starting note with frequency=" + juce::String(frequency) + 
            "Hz, velocity=" + juce::String(velocity) + 
            ", level=" + juce::String(level) + 
            ", gain=" + juce::String(gain) +
            ", octave=" + juce::String(octaveShift) +
            ", semitone=" + juce::String(semitoneShift) +
            ", detune=" + juce::String(detuneValue));
    }

    void stopNote(float /*velocity*/, bool allowTailOff) override
    {
        if (allowTailOff)
        {
            if (tailOff == 0.0)
                tailOff = 1.0;
        }
        else
        {
            clearCurrentNote();
            isPlaying = false;
            currentMidiNote = -1; // 清除当前MIDI音符编号
        }
    }

    void setWaveform(int newWaveform)
    {
        waveform = newWaveform;
    }

    void setGain(float newGain)
    {
        // 确保gain参数在有效范围内
        gain = juce::jlimit(0.0f, 1.0f, newGain);
    }

    void setDetune(float newDetune)
    {
        // 记录原来的值，用于对比
        float oldDetune = detuneValue;
        
        // 设置新的detune值（参数范围已在AudioParameter中限制）
        detuneValue = newDetune;
        
        // 如果值确实发生了变化，才更新频率
        if (oldDetune != detuneValue)
        {
            DBG("OscillatorVoice::setDetune - Changed from " + juce::String(oldDetune) + 
                " to " + juce::String(detuneValue) + " cents");
                
            // 更新正在播放的音符的频率
            updateFrequencyIfPlaying();
        }
    }
    
    void setSemitone(int newSemitone)
    {
        // 确保semitone参数在有效范围内 (-24 到 +24 半音)
        semitoneShift = juce::jlimit(-24, 24, newSemitone);
        updateFrequencyIfPlaying();
    }
    
    void setOctave(int newOctave)
    {
        // 确保octave参数在有效范围内 (-2 到 +2 八度)
        octaveShift = juce::jlimit(-2, 2, newOctave);
        updateFrequencyIfPlaying();
    }

    void pitchWheelMoved(int) override {}
    void controllerMoved(int, int) override {}

    void renderNextBlock(juce::AudioBuffer<float>& outputBuffer, int startSample, int numSamples) override
    {
        if (!isPlaying)
            return;

        // 输出声音生成调试信息
        // if (numSamples > 0)
        // {
        //     static int debugCounter = 0;
        //     if (++debugCounter % 50 == 0) // 限制打印频率
        //     {
        //         DBG("OscillatorVoice: Rendering audio with gain=" + juce::String(gain) + 
        //             ", level=" + juce::String(level) + 
        //             ", waveform=" + juce::String(waveform));
        //     }
        // }

        for (int sample = 0; sample < numSamples; ++sample)
        {
            float currentSample = 0.0f;
            
            // 使用DSP振荡器生成波形
            switch (waveform)
            {
                case 0: // Sine
                    currentSample = sineOsc.processSample(0.0f);
                    break;
                case 1: // Square
                    currentSample = squareOsc.processSample(0.0f);
                    // 方波音量较大，适当降低音量避免失真
                    currentSample *= 0.7f;
                    break;
                case 2: // Saw
                    currentSample = sawOsc.processSample(0.0f);
                    break;
            }

            if (tailOff > 0.0)
            {
                // 使用指数衰减来实现更平滑的淡出
                currentSample *= level * gain * float(tailOff);
                tailOff *= 0.99;  // 调整这个值可以改变淡出速度
                
                if (tailOff <= 0.005)
                {
                    clearCurrentNote();
                    isPlaying = false;
                    break;
                }
            }
            else
            {
                currentSample *= level * gain;
            }
            
            for (int channel = 0; channel < outputBuffer.getNumChannels(); ++channel)
                outputBuffer.addSample(channel, startSample + sample, currentSample);
        }
    }

    void prepare(double sampleRate)
    {
        // 准备DSP处理器
        juce::dsp::ProcessSpec spec;
        spec.sampleRate = sampleRate;
        spec.maximumBlockSize = 512; // 通常的音频块大小
        spec.numChannels = 2;       // 立体声
        
        sineOsc.prepare(spec);
        sawOsc.prepare(spec);
        squareOsc.prepare(spec);
        
        // 重置所有振荡器
        sineOsc.reset();
        sawOsc.reset();
        squareOsc.reset();
        
        // 确保所有振荡器的频率设置正确
        if (frequency > 0.0)
        {
            sineOsc.setFrequency(frequency, sampleRate);
            sawOsc.setFrequency(frequency, sampleRate);
            squareOsc.setFrequency(frequency, sampleRate);
        }
    }

private:
    // 更新已经播放中音符的频率
    void updateFrequencyIfPlaying()
    {
        if (isPlaying && currentMidiNote >= 0)
        {
            // 保存原始音符频率用于对比
            float originalNote = static_cast<float>(currentMidiNote);
            float originalFrequency = juce::MidiMessage::getMidiNoteInHertz(originalNote);
            
            // 计算调整后的音符和频率
            float adjustedNote = static_cast<float>(currentMidiNote) + 
                               octaveShift * 12.0f + 
                               semitoneShift + 
                               detuneValue / 100.0f;
                               
            frequency = juce::MidiMessage::getMidiNoteInHertz(adjustedNote);
            
            // 计算频率变化百分比
            float freqRatio = frequency / originalFrequency;
            float centsDifference = 1200.0f * std::log2(freqRatio);
            
            auto sampleRate = getSampleRate();
            sineOsc.setFrequency(frequency, sampleRate);
            sawOsc.setFrequency(frequency, sampleRate);
            squareOsc.setFrequency(frequency, sampleRate);
            
            
        }
    }

    double frequency = 0.0;
    double tailOff = 0.0;
    float level = 0.0f;
    float gain = 0.5f;
    int waveform = 0;
    bool isPlaying = false;
    int currentMidiNote = -1;
    
    // 音高调整参数
    float detuneValue = 0.0f;     // 失谐/微调 (-100 到 +100 音分)
    int semitoneShift = 0;         // 半音调整 (-24 到 +24 半音)
    int octaveShift = 0;           // 八度调整 (-2 到 +2 八度)
    
    // JUCE DSP振荡器
    juce::dsp::Oscillator<float> sineOsc;
    juce::dsp::Oscillator<float> sawOsc;
    juce::dsp::Oscillator<float> squareOsc;
};

//==============================================================================
class OscillatorProcessor : public juce::AudioProcessor,
                          public juce::AudioProcessorValueTreeState::Listener
{
public:
    OscillatorProcessor();
    ~OscillatorProcessor() override;
    
    // 基本处理器功能
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>&, juce::MidiBuffer&) override;
    
    // UI相关
    bool hasEditor() const override { return false; }
    juce::AudioProcessorEditor* createEditor() override { return nullptr; }

    // 基本信息
    const juce::String getName() const override { return "Oscillator"; }
    bool acceptsMidi() const override { return true; }
    bool producesMidi() const override { return false; }
    double getTailLengthSeconds() const override { return 0.0; }

    // 程序管理
    int getNumPrograms() override { return 1; }
    int getCurrentProgram() override { return 0; }
    void setCurrentProgram(int) override {}
    const juce::String getProgramName(int) override { return {}; }
    void changeProgramName(int, const juce::String&) override {}

    // 总线布局支持
    bool isBusesLayoutSupported(const BusesLayout& layouts) const override
    {
        // 支持无输入和双声道输出
        if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo())
            return false;

        // 检查输入总线是否为空
        if (!layouts.getMainInputChannelSet().isDisabled())
            return false;

        return true;
    }

    bool isMidiEffect() const override { return false; }

    // 状态管理
    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

    // 参数管理
    juce::AudioProcessorValueTreeState& getParameterTree() { return *parameters; }

    // MIDI键盘状态
    juce::MidiKeyboardState& getKeyboardState() { return keyboardState; }

    // MIDI端口管理
    bool isMidiInputEnabled() const { return midiInputEnabled; }
    void enableMidiInput(bool enable) { midiInputEnabled = enable; }
    void setMidiInputNodeId(juce::AudioProcessorGraph::NodeID id) { midiInputNodeId = id; }
    juce::AudioProcessorGraph::NodeID getMidiInputNodeId() const { return midiInputNodeId; }

    // MIDI通道控制
    int getMidiChannel() const { return midiChannel; }
    void setMidiChannel(int newChannel) { midiChannel = juce::jlimit(1, 16, newChannel); }

    // 处理MIDI事件
    void handleMidiEvent(const juce::MidiMessage& message);

    // 参数监听器
    void parameterChanged(const juce::String& parameterID, float newValue) override;

private:
    // 创建参数布局
    static juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout();
    
    // 新增的私有方法，用于在音频线程中安全地更新voice参数
    void updateVoiceParameters();

    // 参数管理 - 在构造函数中初始化
    std::unique_ptr<juce::AudioProcessorValueTreeState> parameters{
        std::make_unique<juce::AudioProcessorValueTreeState>(*this, nullptr, "Parameters", createParameterLayout())
    };

    juce::MidiKeyboardState keyboardState;
    juce::Synthesiser synth;
    
    // MIDI 和音频处理状态
    std::atomic<bool> midiInputEnabled{false};
    juce::AudioProcessorGraph::NodeID midiInputNodeId;
    std::set<int> activeNotes;  // 当前活动的MIDI音符
    int midiChannel = 0;        // 0表示接收所有通道，1-16表示特定通道
    double currentAngle{0.0};   // 当前相位角度

    // 参数状态
    std::atomic<float> currentGain{0.5f};
    std::atomic<int> currentWaveform{0};
    std::atomic<float> currentFrequency{440.0f};
    std::atomic<float> currentDetune{0.0f};   // 失谐/微调
    std::atomic<int> currentSemitone{0};      // 半音调整
    std::atomic<int> currentOctave{0};        // 八度调整

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(OscillatorProcessor)
}; 