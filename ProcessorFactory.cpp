#include "ProcessorFactory.h"
#include "OscillatorProcessor.h"
#include "TestOscillatorProcessor.h"
#include "AudioInputProcessor.h"
#include "AudioOutputProcessor.h"
#include "MidiInputProcessor.h"
#include "MidiOutputProcessor.h"
#include "TestMidiOutProcessor.h"
#include "DelayProcessor.h"
#include "EnvelopeGeneratorProcessor.h"
#include "MixerProcessor.h"

JUCE_BEGIN_IGNORE_WARNINGS_MSVC(4100 4244 4996)

std::unique_ptr<juce::AudioProcessor> ProcessorFactory::create(const juce::String& moduleType, 
                                                              juce::AudioDeviceManager& deviceManager)
{
    // First check full class names
    if (moduleType == "OscillatorProcessor")
        return std::make_unique<OscillatorProcessor>();
    else if (moduleType == "TestOscillatorProcessor")
        return std::make_unique<TestOscillatorProcessor>();
    else if (moduleType == "AudioInputProcessor")
        return std::make_unique<AudioInputProcessor>(deviceManager);
    else if (moduleType == "AudioOutputProcessor")
        return std::make_unique<AudioOutputProcessor>(deviceManager);
    else if (moduleType == "MidiInputProcessor")
        return std::make_unique<MidiInputProcessor>(deviceManager);
    else if (moduleType == "MidiOutputProcessor")
        return std::make_unique<MidiOutputProcessor>(deviceManager);
    else if (moduleType == "TestMidiOutProcessor")
        return std::make_unique<TestMidiOutProcessor>();
    else if (moduleType == "DelayProcessor")
        return std::make_unique<DelayProcessor>();
    else if (moduleType == "EnvelopeGeneratorProcessor")
        return std::make_unique<EnvelopeGeneratorProcessor>();
    else if (moduleType == "MixerProcessor")
        return std::make_unique<MixerProcessor>();
    
    // Then check shorthand types
    // Handle shorthand types passed from ModuleManager::parseModuleCommand
    else if (moduleType.equalsIgnoreCase("oscillator"))
        return std::make_unique<OscillatorProcessor>();
    else if (moduleType.equalsIgnoreCase("testosc"))
        return std::make_unique<TestOscillatorProcessor>();
    else if (moduleType.equalsIgnoreCase("input"))
        return std::make_unique<AudioInputProcessor>(deviceManager);
    else if (moduleType.equalsIgnoreCase("output"))
        return std::make_unique<AudioOutputProcessor>(deviceManager);
    else if (moduleType.equalsIgnoreCase("midiin"))
        return std::make_unique<MidiInputProcessor>(deviceManager);
    else if (moduleType.equalsIgnoreCase("midiout"))
        return std::make_unique<MidiOutputProcessor>(deviceManager);
    else if (moduleType.equalsIgnoreCase("testmidiout"))
        return std::make_unique<TestMidiOutProcessor>();
    else if (moduleType.equalsIgnoreCase("delay"))
        return std::make_unique<DelayProcessor>();
    else if (moduleType.equalsIgnoreCase("envelopegenerator"))
        return std::make_unique<EnvelopeGeneratorProcessor>();
    else if (moduleType.equalsIgnoreCase("mixer"))
        return std::make_unique<MixerProcessor>();
    
    // If no matching processor type found, return nullptr
    juce::Logger::writeToLog("ProcessorFactory: No processor type found: " + moduleType);
    return nullptr;
}

JUCE_END_IGNORE_WARNINGS_MSVC 