#include "EnvelopeGeneratorProcessor.h"
#include "EnvelopeGeneratorProcessorComponent.h"

//==============================================================================
EnvelopeGeneratorProcessor::EnvelopeGeneratorProcessor()
    : AudioProcessor(BusesProperties()
          .withInput("Audio In", juce::AudioChannelSet::stereo())
          .withOutput("Audio Out", juce::AudioChannelSet::stereo()))
{
    // Add parameter listeners
    parameters->addParameterListener("attack", this);
    parameters->addParameterListener("hold", this);
    parameters->addParameterListener("decay", this);
    parameters->addParameterListener("sustain", this);
    parameters->addParameterListener("release", this);
    
    // Initialize smoothed values
    attackTimeSmoothed.setCurrentAndTargetValue(attackTime.load());
    holdTimeSmoothed.setCurrentAndTargetValue(holdTime.load());
    decayTimeSmoothed.setCurrentAndTargetValue(decayTime.load());
    sustainLevelSmoothed.setCurrentAndTargetValue(sustainLevel.load());
    releaseTimeSmoothed.setCurrentAndTargetValue(releaseTime.load());
    
    // Initialize envelope parameters
    updateEnvelopeParameters();
}

EnvelopeGeneratorProcessor::~EnvelopeGeneratorProcessor()
{
    // Remove parameter listeners
    parameters->removeParameterListener("attack", this);
    parameters->removeParameterListener("hold", this);
    parameters->removeParameterListener("decay", this);
    parameters->removeParameterListener("sustain", this);
    parameters->removeParameterListener("release", this);
}

void EnvelopeGeneratorProcessor::updateEnvelopeParameters()
{
    // Use smoothed values to update envelope parameters
    envelope.setParameters(
        attackTimeSmoothed.getCurrentValue(),
        holdTimeSmoothed.getCurrentValue(),
        decayTimeSmoothed.getCurrentValue(), 
        sustainLevelSmoothed.getCurrentValue(),
        releaseTimeSmoothed.getCurrentValue()
    );
}

void EnvelopeGeneratorProcessor::parameterChanged(const juce::String& parameterID, float newValue)
{
    if (parameterID == "attack")
    {
        attackTime.store(newValue);
        attackTimeSmoothed.setTargetValue(newValue);
    }
    else if (parameterID == "hold")
    {
        holdTime.store(newValue);
        holdTimeSmoothed.setTargetValue(newValue);
    }
    else if (parameterID == "decay")
    {
        decayTime.store(newValue);
        decayTimeSmoothed.setTargetValue(newValue);
    }
    else if (parameterID == "sustain")
    {
        sustainLevel.store(newValue);
        sustainLevelSmoothed.setTargetValue(newValue);
    }
    else if (parameterID == "release")
    {
        releaseTime.store(newValue);
        releaseTimeSmoothed.setTargetValue(newValue);
    }
    
    // We'll update parameters in the process block using smoothed values
}

juce::AudioProcessorValueTreeState::ParameterLayout EnvelopeGeneratorProcessor::createParameterLayout()
{
    juce::AudioProcessorValueTreeState::ParameterLayout layout;
    
    // Create envelope parameters
    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "attack",
        "Attack",
        juce::NormalisableRange<float>(0.001f, 5.0f, 0.001f, 0.5f), // Using logarithmic scaling
        0.1f,
        juce::String(),
        juce::AudioProcessorParameter::genericParameter,
        [](float value, int) { return juce::String(value, 3) + " s"; }));
        
    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "hold",
        "Hold",
        juce::NormalisableRange<float>(0.0f, 5.0f, 0.001f, 0.5f),
        0.0f,
        juce::String(),
        juce::AudioProcessorParameter::genericParameter,
        [](float value, int) { return juce::String(value, 3) + " s"; }));
        
    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "decay",
        "Decay",
        juce::NormalisableRange<float>(0.001f, 5.0f, 0.001f, 0.5f),
        0.3f,
        juce::String(),
        juce::AudioProcessorParameter::genericParameter,
        [](float value, int) { return juce::String(value, 3) + " s"; }));
        
    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "sustain",
        "Sustain",
        juce::NormalisableRange<float>(0.0f, 1.0f, 0.01f),
        0.7f,
        juce::String(),
        juce::AudioProcessorParameter::genericParameter,
        [](float value, int) { return juce::String(int(value * 100)) + "%"; }));
        
    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "release",
        "Release",
        juce::NormalisableRange<float>(0.001f, 5.0f, 0.001f, 0.5f),
        0.2f,
        juce::String(),
        juce::AudioProcessorParameter::genericParameter,
        [](float value, int) { return juce::String(value, 3) + " s"; }));
        
    return layout;
}

void EnvelopeGeneratorProcessor::prepareToPlay(double newSampleRate, int samplesPerBlock)
{
    juce::ignoreUnused(samplesPerBlock);
    
    sampleRate = newSampleRate;
    
    envelope.prepare(sampleRate);
    
    // Initialize smoothed values
    const float smoothingTimeSec = smoothingTimeMs * 0.001f;
    
    attackTimeSmoothed.reset(sampleRate, smoothingTimeSec);
    holdTimeSmoothed.reset(sampleRate, smoothingTimeSec);
    decayTimeSmoothed.reset(sampleRate, smoothingTimeSec);
    sustainLevelSmoothed.reset(sampleRate, smoothingTimeSec);
    releaseTimeSmoothed.reset(sampleRate, smoothingTimeSec);
    
    // Set initial smoothed values
    attackTimeSmoothed.setCurrentAndTargetValue(attackTime.load());
    holdTimeSmoothed.setCurrentAndTargetValue(holdTime.load());
    decayTimeSmoothed.setCurrentAndTargetValue(decayTime.load());
    sustainLevelSmoothed.setCurrentAndTargetValue(sustainLevel.load());
    releaseTimeSmoothed.setCurrentAndTargetValue(releaseTime.load());
    
    // Update envelope parameters
    updateEnvelopeParameters();
}

void EnvelopeGeneratorProcessor::releaseResources()
{
    // Release resources
    envelope.reset();
}

void EnvelopeGeneratorProcessor::handleMidiEvent(const juce::MidiMessage& message)
{
    // Process MIDI messages
    bool noteWasOn = gateOn;
    
    // Handle note-on messages
    if (message.isNoteOn())
    {
        int noteNumber = message.getNoteNumber();
        
        // Check MIDI channel
        if (midiChannel == 0 || message.getChannel() == midiChannel)
        {
            // Add to active notes list
            activeNotes.insert(noteNumber);
            
            // If no note was pressed before, trigger envelope
            if (!noteWasOn)
            {
                envelope.noteOn();
                gateOn = true;
                DBG("EnvelopeGenerator Note-On: Note=" + juce::String(noteNumber));
            }
        }
    }
    // Handle note-off messages
    else if (message.isNoteOff())
    {
        int noteNumber = message.getNoteNumber();
        
        // Check MIDI channel
        if (midiChannel == 0 || message.getChannel() == midiChannel)
        {
            // Remove from active notes list
            activeNotes.erase(noteNumber);
            
            // If no more active notes, trigger release phase
            if (activeNotes.empty() && noteWasOn)
            {
                envelope.noteOff();
                gateOn = false;
                DBG("EnvelopeGenerator Note-Off: Note=" + juce::String(noteNumber));
            }
        }
    }
    // Handle all notes off or controller reset messages
    else if (message.isAllNotesOff() || message.isAllSoundOff())
    {
        // Check MIDI channel
        if (midiChannel == 0 || message.getChannel() == midiChannel)
        {
            // Clear active notes list
            activeNotes.clear();
            
            // If notes were playing, trigger release phase
            if (noteWasOn)
            {
                envelope.noteOff();
                gateOn = false;
                DBG("EnvelopeGenerator All-Notes-Off");
            }
        }
    }
}

void EnvelopeGeneratorProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    juce::ScopedNoDenormals noDenormals;
    
    // Process MIDI messages
    for (const auto metadata : midiMessages)
    {
        auto message = metadata.getMessage();
        handleMidiEvent(message);
    }
    
    // Check if parameters need smoothing
    bool needsUpdate = attackTimeSmoothed.isSmoothing() || 
                      holdTimeSmoothed.isSmoothing() || 
                      decayTimeSmoothed.isSmoothing() || 
                      sustainLevelSmoothed.isSmoothing() || 
                      releaseTimeSmoothed.isSmoothing();
    
    // 获取初始包络值
    float currentEnvValue = envelope.getCurrentLevel();
    
    // 更新总体包络电平 - 保留向后兼容性
    currentEnvelopeLevel.store(currentEnvValue);
    
    // 用于跟踪实际音频输出电平
    float leftChannelPeak = 0.0f;
    float rightChannelPeak = 0.0f;
    
    // Process input audio
    for (int channel = 0; channel < buffer.getNumChannels(); ++channel)
    {
        auto* channelData = buffer.getWritePointer(channel);
        
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            // If parameters are changing, update envelope parameters
            if (needsUpdate)
            {
                // Get next smoothed values
                float attack = attackTimeSmoothed.getNextValue();
                float hold = holdTimeSmoothed.getNextValue();
                float decay = decayTimeSmoothed.getNextValue();
                float sustain = sustainLevelSmoothed.getNextValue();
                float release = releaseTimeSmoothed.getNextValue();
                
                // Update envelope parameters
                envelope.setParameters(attack, hold, decay, sustain, release);
            }
            
            // 获取下一个包络样本值
            float envelopeValue = envelope.getNextSample();
            
            // 应用包络到输入音频
            float originalSample = channelData[sample];
            channelData[sample] *= envelopeValue;
            
            // 跟踪各通道的峰值电平
            if (channel == 0)
            {
                leftChannelPeak = juce::jmax(leftChannelPeak, std::abs(channelData[sample]));
            }
            else if (channel == 1)
            {
                rightChannelPeak = juce::jmax(rightChannelPeak, std::abs(channelData[sample]));
            }
        }
    }
    
    // 更新UI显示的电平值 - 使用实际音频输出的峰值
    leftChannelEnvelopeLevel.store(leftChannelPeak);
    rightChannelEnvelopeLevel.store(rightChannelPeak);
}

juce::AudioProcessorEditor* EnvelopeGeneratorProcessor::createEditor()
{
    return new EnvelopeGeneratorProcessorComponent(*this);
}

void EnvelopeGeneratorProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    auto state = parameters->copyState();
    std::unique_ptr<juce::XmlElement> xml(state.createXml());
    copyXmlToBinary(*xml, destData);
}

void EnvelopeGeneratorProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    std::unique_ptr<juce::XmlElement> xmlState(getXmlFromBinary(data, sizeInBytes));
    
    if (xmlState != nullptr)
    {
        if (xmlState->hasTagName(parameters->state.getType()))
        {
            parameters->replaceState(juce::ValueTree::fromXml(*xmlState));
            
            // Update parameter values
            attackTime.store(parameters->getRawParameterValue("attack")->load());
            holdTime.store(parameters->getRawParameterValue("hold")->load());
            decayTime.store(parameters->getRawParameterValue("decay")->load());
            sustainLevel.store(parameters->getRawParameterValue("sustain")->load());
            releaseTime.store(parameters->getRawParameterValue("release")->load());
            
            // Update smoothed values
            attackTimeSmoothed.setCurrentAndTargetValue(attackTime.load());
            holdTimeSmoothed.setCurrentAndTargetValue(holdTime.load());
            decayTimeSmoothed.setCurrentAndTargetValue(decayTime.load());
            sustainLevelSmoothed.setCurrentAndTargetValue(sustainLevel.load());
            releaseTimeSmoothed.setCurrentAndTargetValue(releaseTime.load());
            
            // Update envelope parameters
            updateEnvelopeParameters();
        }
    }
} 