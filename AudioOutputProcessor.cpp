﻿#include "AudioOutputProcessor.h"

// Constructor: Register listener
AudioOutputProcessor::AudioOutputProcessor(juce::AudioDeviceManager& deviceManager)
    : AudioGraphIOProcessor(juce::AudioProcessorGraph::AudioGraphIOProcessor::audioOutputNode),
      deviceManager(deviceManager),
      parameters(*this, nullptr, "PARAMETERS", createParameterLayout())
{
    parameters.addParameterListener("masterVolume", this);
    // Initialize atomic member from parameter state
    if (auto* volParam = parameters.getRawParameterValue("masterVolume"))
        masterVolume.store(volParam->load());
    else
        masterVolume.store(1.0f); // Fallback if parameter not found initially (should not happen)
}

// Destructor: Unregister listener
AudioOutputProcessor::~AudioOutputProcessor()
{
    parameters.removeParameterListener("masterVolume", this);
}

void AudioOutputProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    juce::ignoreUnused(sampleRate, samplesPerBlock);
    // Initialize atomic member from parameter state on prepareToPlay as well
    if (auto* volParam = parameters.getRawParameterValue("masterVolume"))
        masterVolume.store(volParam->load());
    else
        masterVolume.store(1.0f); // Fallback
}

void AudioOutputProcessor::releaseResources()
{
    // 释放资源
}

// Listener callback implementation
void AudioOutputProcessor::parameterChanged(const juce::String& parameterID, float newValue)
{
    if (parameterID == "masterVolume")
    {
        masterVolume.store(newValue);
        // DBG("Master Volume Parameter Changed: " + juce::String(newValue)); // Optional Debug
    }
}

void AudioOutputProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    juce::ignoreUnused(midiMessages);
    juce::ScopedNoDenormals noDenormals;

    const int numOutputChannels = buffer.getNumChannels();
    const int numSamples = buffer.getNumSamples();

    // Get the volume value from the atomic cache
    float currentVolume = masterVolume.load();
    // DBG print the current volume value
    DBG("AOU ProcessBlock - Current Volume: " + juce::String(currentVolume));

    // 应用音量
    for (int channel = 0; channel < numOutputChannels; ++channel)
    {
        auto* channelData = buffer.getWritePointer(channel);
        for (int sample = 0; sample < numSamples; ++sample)
        {
            channelData[sample] *= currentVolume;
        }
    }

    // 调试输出
    if (numSamples > 0 && numOutputChannels > 0)
    {
        float rms = 0.0f;
        auto* channelData = buffer.getReadPointer(0);
        for (int i = 0; i < numSamples; ++i)
        {
            rms += channelData[i] * channelData[i];
        }
        rms = std::sqrt(rms / numSamples);
        
        if (rms > 0.001f)
        {
            DBG("Audio output level: " + juce::String(rms) + " Volume: " + juce::String(currentVolume));
        }
    }
}

void AudioOutputProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    auto state = parameters.copyState();
    std::unique_ptr<juce::XmlElement> xml(state.createXml());
    copyXmlToBinary(*xml, destData);
}

void AudioOutputProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    std::unique_ptr<juce::XmlElement> xmlState(getXmlFromBinary(data, sizeInBytes));
    if (xmlState != nullptr)
    {
        auto state = juce::ValueTree::fromXml(*xmlState);
        parameters.replaceState(state);
    }
} 