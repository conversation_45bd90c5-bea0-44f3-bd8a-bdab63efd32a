#pragma once

#include <JuceHeader.h>

class TestMidiOutProcessor : public juce::AudioProcessor
{
public:
    TestMidiOutProcessor();
    ~TestMidiOutProcessor() override;

    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>&, juce::MidiBuffer&) override;

    juce::AudioProcessorEditor* createEditor() override { return nullptr; }
    bool hasEditor() const override { return false; }

    const juce::String getName() const override { return "TestMidiOut"; }

    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return true; }
    bool isMidiEffect() const override { return true; }
    double getTailLengthSeconds() const override { return 0.0; }

    int getNumPrograms() override { return 1; }
    int getCurrentProgram() override { return 0; }
    void setCurrentProgram(int) override {}
    const juce::String getProgramName(int) override { return {}; }
    void changeProgramName(int, const juce::String&) override {}

    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

private:
    enum class State {
        Playing,    // Playing note
        Silence     // Silent phase
    };

    double currentSampleRate = 44100.0;
    int samplesUntilNextNote = 0;
    State currentState = State::Silence;
    const int midiChannel = 1;      // MIDI Channel 1
    const int midiNote = 60;        // Middle C
    const int midiVelocity = 100;   // Note velocity
    const int noteDurationSamples = 44100;    // 1 second
    const int silenceDurationSamples = 44100; // 1 second

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(TestMidiOutProcessor)
}; 