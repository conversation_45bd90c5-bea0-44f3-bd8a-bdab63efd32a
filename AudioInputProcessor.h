﻿#pragma once

#include <JuceHeader.h>

class AudioInputProcessor : public juce::AudioProcessorGraph::AudioGraphIOProcessor
{
public:
    explicit AudioInputProcessor(juce::AudioDeviceManager& _deviceManager)
        : AudioGraphIOProcessor(juce::AudioProcessorGraph::AudioGraphIOProcessor::audioInputNode)
    {
        juce::ignoreUnused(_deviceManager);
    }

    // 基本处理器功能
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages) override;
    
    
    // 状态管理
    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

    // UI相关
    juce::AudioProcessorEditor* createEditor() override { return nullptr; }
    bool hasEditor() const override { return false; }

    // 基本信息
    const juce::String getName() const override { return "Audio Input"; }
    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return false; }

    // 增益控制
    float getInputGain() const { return inputGain; }
    void setInputGain(float newGain) { inputGain = newGain; }

private:
    float inputGain = 1.0f;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioInputProcessor)
}; 