#pragma once

#include <JuceHeader.h>
#include "TestOscillatorProcessor.h"

class TestOscillatorProcessorComponent : public juce::Component
{
public:
    TestOscillatorProcessorComponent(TestOscillatorProcessor& p)
        : processor(p)
    {
        setSize(200, 100);
        
        // 添加标签显示固定频率
        addAndMakeVisible(frequencyLabel);
        frequencyLabel.setText("440 Hz", juce::dontSendNotification);
        frequencyLabel.setJustificationType(juce::Justification::centred);
        
        // 添加标签显示固定音量
        addAndMakeVisible(gainLabel);
        gainLabel.setText("Gain: 0.5", juce::dontSendNotification);
        gainLabel.setJustificationType(juce::Justification::centred);
    }

    ~TestOscillatorProcessorComponent() override = default;

    void paint(juce::Graphics& g) override
    {
        g.fillAll(juce::Colours::darkgrey);
        g.setColour(juce::Colours::white);
        g.drawRect(getLocalBounds(), 1);
        
        g.setFont(15.0f);
        g.drawText("Test Oscillator", getLocalBounds().removeFromTop(30),
                  juce::Justification::centred, true);
    }

    void resized() override
    {
        auto bounds = getLocalBounds();
        
        // 顶部标题区域
        bounds.removeFromTop(30);
        
        // 频率标签
        frequencyLabel.setBounds(bounds.removeFromTop(30));
        
        // 音量标签
        gainLabel.setBounds(bounds.removeFromTop(30));
    }

private:
    TestOscillatorProcessor& processor;
    juce::Label frequencyLabel;
    juce::Label gainLabel;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(TestOscillatorProcessorComponent)
}; 