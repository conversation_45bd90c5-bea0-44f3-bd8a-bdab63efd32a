#include "ModuleUIManager.h"
#include "ModuleManager.h"
#include "ProcessorFactory.h"
#include "OscillatorProcessorComponent.h"
#include "TestOscillatorProcessorComponent.h"
#include "AudioInputProcessorComponent.h"
#include "AudioOutputProcessorComponent.h"
#include "MidiInputProcessorComponent.h"
#include "MidiOutputProcessorComponent.h"
#include "TestMidiOutProcessorComponent.h"
#include "DelayProcessorComponent.h"
#include "EnvelopeGeneratorProcessorComponent.h"
#include "MixerProcessorComponent.h"
// #include <memory>

// Helper template function to register component factories
template<typename ProcessorType, typename ComponentType>
void registerComponentFactory(std::map<juce::String, ComponentFactory>& factories, 
                             const ProcessorType& processorInstance)
{
    factories[processorInstance.getName()] = [](juce::AudioProcessor* processor) -> std::unique_ptr<juce::Component> {
        auto* typedProcessor = dynamic_cast<ProcessorType*>(processor);
        if (typedProcessor != nullptr) {
            auto component = std::make_unique<ComponentType>(*typedProcessor);
            component->setInterceptsMouseClicks(true, true);
            return component;
        }
        return nullptr;
    };
}

ModuleUIManager::ModuleUIManager(ModuleManager& manager)
    : moduleManager(manager)
{
    // Register component factories for all processor types
    registerComponentFactory<OscillatorProcessor, OscillatorProcessorComponent>(
        componentFactories, OscillatorProcessor());
        
    registerComponentFactory<TestOscillatorProcessor, TestOscillatorProcessorComponent>(
        componentFactories, TestOscillatorProcessor());
        
    registerComponentFactory<AudioInputProcessor, AudioInputProcessorComponent>(
        componentFactories, AudioInputProcessor(moduleManager.deviceManager));
        
    registerComponentFactory<AudioOutputProcessor, AudioOutputProcessorComponent>(
        componentFactories, AudioOutputProcessor(moduleManager.deviceManager));
        
    registerComponentFactory<MidiInputProcessor, MidiInputProcessorComponent>(
        componentFactories, MidiInputProcessor(moduleManager.deviceManager));
        
    registerComponentFactory<MidiOutputProcessor, MidiOutputProcessorComponent>(
        componentFactories, MidiOutputProcessor(moduleManager.deviceManager));
        
    registerComponentFactory<TestMidiOutProcessor, TestMidiOutProcessorComponent>(
        componentFactories, TestMidiOutProcessor());
        
    registerComponentFactory<DelayProcessor, DelayProcessorComponent>(
        componentFactories, DelayProcessor());
        
    registerComponentFactory<EnvelopeGeneratorProcessor, EnvelopeGeneratorProcessorComponent>(
        componentFactories, EnvelopeGeneratorProcessor());
        
    registerComponentFactory<MixerProcessor, MixerProcessorComponent>(
        componentFactories, MixerProcessor());
}
ModuleUIManager::~ModuleUIManager()
{
    componentFactories.clear();
}

std::unique_ptr<juce::Component> ModuleUIManager::createComponentForProcessor(juce::AudioProcessor* processor)
{
    if (processor == nullptr)
        return nullptr;
        
    auto procName = processor->getName();
    bool factoryFound = componentFactories.count(procName) > 0;
    
    if (factoryFound)
    {
        // Create component using factory
        auto component = componentFactories[procName](processor);
        
        if (component == nullptr)
        {
            DBG("Factory function returned null component for processor: " + procName);
            component = std::make_unique<juce::GenericAudioProcessorEditor>(processor);
        }
        
        return component;
    }
    else
    {
        DBG("No component factory found for processor type: " + procName + ", using generic editor");
        return std::make_unique<juce::GenericAudioProcessorEditor>(processor);
    }
}

void ModuleUIManager::layoutModules(juce::Component* parentComponent)
{
    if (parentComponent == nullptr)
    {
        DBG("Error: Parent component is null, cannot layout modules");
        return;
    }

    // Store parent component reference
    moduleUIsContainer = parentComponent;
    
    DBG("Laying out modules in parent component. Parent bounds: " + 
        juce::String(parentComponent->getWidth()) + "x" + 
        juce::String(parentComponent->getHeight()) + " at " +
        juce::String(parentComponent->getX()) + "," + 
        juce::String(parentComponent->getY()));
    
    // Layout modules in a grid pattern with dynamic sizing
    int currentX = margin;
    int currentY = margin;
    int rowMaxHeight = 0;
    int containerWidth = parentComponent->getWidth();
    
    DBG("Total module UIs to layout: " + juce::String(moduleManager.moduleUIs.size()));
    
    // Add and position each module component
    for (auto& moduleUI : moduleManager.moduleUIs)
    {
        if (moduleUI && moduleUI->component != nullptr)
        {
            positionModule(moduleUI.get(), currentX, currentY, rowMaxHeight, containerWidth);
        }
    }
    
    // Check component visibility after layout
    DBG("After layout, checking component visibility:");
    for (auto& moduleUI : moduleManager.moduleUIs)
    {
        if (moduleUI && moduleUI->component != nullptr)
        {
            DBG("Module " + moduleUI->uniqueName + 
                ": Visible=" + juce::String(moduleUI->component->isVisible() ? 1 : 0) + 
                ", Enabled=" + juce::String(moduleUI->component->isEnabled() ? 1 : 0));
        }
    }
    
    // Force a repaint to ensure everything is displayed correctly
    parentComponent->repaint();
}

void ModuleUIManager::positionModule(ModuleUI* moduleUI, int& currentX, int& currentY, 
                                    int& rowMaxHeight, int containerWidth)
{
    if (moduleUI == nullptr || moduleUI->component == nullptr)
        return;
        
    int moduleWidth = moduleUI->bounds.getWidth();
    int moduleHeight = moduleUI->bounds.getHeight();

    // Check if the module fits in the current row
    if (currentX + moduleWidth + margin > containerWidth - margin && currentX != margin)
    {
        // Move to the next row
        currentX = margin;
        currentY += rowMaxHeight + margin;
        rowMaxHeight = 0;
    }

    // Position component
    moduleUI->component->setBounds(currentX, currentY, moduleWidth, moduleHeight);
    moduleUI->bounds = {currentX, currentY, moduleWidth, moduleHeight};
    
    DBG("Positioned module: " + moduleUI->uniqueName + " at " + 
        juce::String(currentX) + "," + juce::String(currentY) + " size " + 
        juce::String(moduleWidth) + "x" + juce::String(moduleHeight));
    
    // Update for the next module
    currentX += moduleWidth + margin;
    rowMaxHeight = juce::jmax(rowMaxHeight, moduleHeight);
    
    // Ensure component is visible and interactive
    moduleUI->component->setVisible(true);
    moduleUI->component->setInterceptsMouseClicks(true, true);
    
    // Move the module UI component to the front to ensure interactivity
    moduleUI->component->toFront(false);
}

void ModuleUIManager::addComponentToContainer(ModuleUI* moduleUI)
{
    if (moduleUIsContainer != nullptr && moduleUI != nullptr && moduleUI->component != nullptr)
    {
        // Add component to module system and make it visible
        moduleUIsContainer->addAndMakeVisible(moduleUI->component.get());
        DBG("Added UI component for " + moduleUI->uniqueName + " to moduleUIsContainer");
    }
}

std::unique_ptr<ModuleUI> ModuleUIManager::createModuleUI(juce::AudioProcessor* processor, 
                                                         const juce::String& moduleType,
                                                         const juce::String& moduleName,
                                                         juce::AudioProcessorGraph::NodeID nodeId)
{
    if (processor == nullptr)
        return nullptr;
        
    auto moduleUI = std::make_unique<ModuleUI>();
    moduleUI->type = moduleType;
    moduleUI->uniqueName = moduleName;
    moduleUI->nodeId = nodeId;
    
    // Create UI component for processor
    moduleUI->component = createComponentForProcessor(processor);
    
    if (moduleUI->component != nullptr)
    {
        // Get the size set within the component's constructor or initial setup
        auto componentWidth = moduleUI->component->getWidth();
        auto componentHeight = moduleUI->component->getHeight();

        // If component size is 0, set a default rectangular size
        if (componentWidth == 0 || componentHeight == 0)
        {
            componentWidth = 300; // Default width
            componentHeight = 200; // Default height
            moduleUI->component->setSize(componentWidth, componentHeight);
            DBG("Component size was 0, setting default size for " + moduleName + ": " + 
                juce::String(componentWidth) + "x" + juce::String(componentHeight));
        }

        // Update moduleUI->bounds with the component's actual size
        moduleUI->bounds.setSize(componentWidth, componentHeight);
    }
    
    return moduleUI;
} 