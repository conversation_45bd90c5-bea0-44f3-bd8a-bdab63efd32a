﻿#include "ModuleSystemComponent.h"
#include "OscillatorProcessor.h"
#include "ModuleManager.h"

ModuleSystemComponent::ModuleSystemComponent(ModuleManager& mm)
    : moduleManager(mm)
{
    audioGraph = std::make_unique<juce::AudioProcessorGraph>();
}

void ModuleSystemComponent::resized()
{
    auto area = getLocalBounds();
    // 避免递归调用，完全不在这里调用layoutModules
    DBG("ModuleSystemComponent::resized - avoiding layout calls");
}

void ModuleSystemComponent::setAudioDeviceManager(juce::AudioDeviceManager& manager)
{
    deviceManager = &manager;

    // Set manager reference without changing audio configuration
    // This avoids losing modules when device changes
    DBG("ModuleSystemComponent::setAudioDeviceManager: Setting new audio device manager");
}

bool ModuleSystemComponent::connectProcessors(
    juce::AudioProcessorGraph::NodeID sourceNodeId, int sourceChannelIndex,
    juce::AudioProcessorGraph::NodeID destNodeId, int destChannelIndex)
{
    if (audioGraph == nullptr)
    {
        DBG("ModuleSystem: Connection failed - audio graph is null");
        return false;
    }

    // Check if source node exists and is valid
    auto sourceNode = audioGraph->getNodeForId(sourceNodeId);
    if (sourceNode == nullptr)
    {
        DBG("ModuleSystem: Connection failed - source node " + juce::String(sourceNodeId.uid) + " not found");
        return false;
    }

    // Check if destination node exists and is valid
    auto destNode = audioGraph->getNodeForId(destNodeId);
    if (destNode == nullptr)
    {
        DBG("ModuleSystem: Connection failed - destination node " + juce::String(destNodeId.uid) + " not found");
        return false;
    }

    // Verify channel indices are valid for the nodes
    if (sourceChannelIndex < 0 || sourceChannelIndex >= sourceNode->getProcessor()->getTotalNumOutputChannels())
    {
        DBG("ModuleSystem: Connection failed - invalid source channel " + juce::String(sourceChannelIndex) +
            " (max: " + juce::String(sourceNode->getProcessor()->getTotalNumOutputChannels() - 1) + ")");
        return false;
    }

    if (destChannelIndex < 0 || destChannelIndex >= destNode->getProcessor()->getTotalNumInputChannels())
    {
        DBG("ModuleSystem: Connection failed - invalid destination channel " + juce::String(destChannelIndex) +
            " (max: " + juce::String(destNode->getProcessor()->getTotalNumInputChannels() - 1) + ")");
        return false;
    }

    DBG("ModuleSystem: Attempting to connect nodes - Source: " + juce::String(sourceNodeId.uid) +
        " (channel " + juce::String(sourceChannelIndex) + ") to Dest: " +
        juce::String(destNodeId.uid) + " (channel " + juce::String(destChannelIndex) + ")");

    // Try to add connection
    bool result = audioGraph->addConnection({ { sourceNodeId, sourceChannelIndex },
                                             { destNodeId, destChannelIndex } });

    if (!result)
    {
        DBG("ModuleSystem: Connection failed - could not add connection to graph");
        // Check for possible reasons
        DBG("  Source outputs: " + juce::String(sourceNode->getProcessor()->getTotalNumOutputChannels()) +
            ", Dest inputs: " + juce::String(destNode->getProcessor()->getTotalNumInputChannels()));
    }
    else
    {
        DBG("ModuleSystem: Connection successful");
    }

    return result;
}

bool ModuleSystemComponent::connectMidiProcessors(
    juce::AudioProcessorGraph::NodeID sourceNodeId,
    juce::AudioProcessorGraph::NodeID destNodeId)
{
    if (audioGraph == nullptr)
    {
        DBG("ModuleSystem: Audio graph is null");
        return false;
    }

    // Check if source node produces MIDI
    auto* sourceProcessor = getProcessorForNode(sourceNodeId);
    if (sourceProcessor == nullptr)
    {
        DBG("ModuleSystem: Source processor not found");
        return false;
    }
    if (!sourceProcessor->producesMidi())
    {
        DBG("ModuleSystem: Source processor does not produce MIDI");
        return false;
    }

    // Check if destination node accepts MIDI
    auto* destProcessor = getProcessorForNode(destNodeId);
    if (destProcessor == nullptr)
    {
        DBG("ModuleSystem: Destination processor not found");
        return false;
    }
    if (!destProcessor->acceptsMidi())
    {
        DBG("ModuleSystem: Destination processor does not accept MIDI");
        return false;
    }

    // Enable MIDI input for oscillator if applicable
    if (auto* oscProcessor = dynamic_cast<OscillatorProcessor*>(destProcessor))
    {
        // DBG("ModuleSystem: Enabling MIDI input for oscillator");
        oscProcessor->enableMidiInput(true);
        oscProcessor->setMidiInputNodeId(sourceNodeId);
    }


    auto result = audioGraph->addConnection({
        { sourceNodeId, juce::AudioProcessorGraph::midiChannelIndex },
        { destNodeId, juce::AudioProcessorGraph::midiChannelIndex }
    });


    return result;
}

bool ModuleSystemComponent::disconnectMidiProcessors(
    juce::AudioProcessorGraph::NodeID sourceNodeId,
    juce::AudioProcessorGraph::NodeID destNodeId)
{
    if (audioGraph == nullptr)
        return false;

    // Remove MIDI connection
    auto result = audioGraph->removeConnection({
        { sourceNodeId, juce::AudioProcessorGraph::midiChannelIndex },
        { destNodeId, juce::AudioProcessorGraph::midiChannelIndex }
    });

    // Disable MIDI input for oscillator if applicable
    if (auto* destProcessor = getProcessorForNode(destNodeId))
    {
        if (auto* oscProcessor = dynamic_cast<OscillatorProcessor*>(destProcessor))
        {
            oscProcessor->enableMidiInput(false);
            oscProcessor->setMidiInputNodeId({});
        }
    }

    return result;
}

bool ModuleSystemComponent::isMidiConnected(
    juce::AudioProcessorGraph::NodeID sourceNodeId,
    juce::AudioProcessorGraph::NodeID destNodeId) const
{
    if (audioGraph == nullptr)
        return false;

    for (auto& connection : audioGraph->getConnections())
    {
        if (connection.source.nodeID == sourceNodeId &&
            connection.destination.nodeID == destNodeId &&
            connection.source.channelIndex == juce::AudioProcessorGraph::midiChannelIndex &&
            connection.destination.channelIndex == juce::AudioProcessorGraph::midiChannelIndex)
        {
            return true;
        }
    }

    return false;
}

void ModuleSystemComponent::disconnectProcessor(juce::AudioProcessorGraph::NodeID nodeId)
{
    if (audioGraph == nullptr)
        return;

    // Remove all connections related to this node
    for (auto connection : audioGraph->getConnections())
    {
        if (connection.source.nodeID == nodeId || connection.destination.nodeID == nodeId)
        {
            audioGraph->removeConnection(connection);
        }
    }
}

juce::AudioProcessor* ModuleSystemComponent::getProcessorForNode(juce::AudioProcessorGraph::NodeID nodeId)
{
    // 通过转换为const版本调用，然后去除const属性返回结果
    return const_cast<juce::AudioProcessor*>(
        static_cast<const ModuleSystemComponent&>(*this).getProcessorForNode(nodeId));
}

juce::AudioProcessor* ModuleSystemComponent::getProcessorForNode(juce::AudioProcessorGraph::NodeID nodeId) const
{
    if (audioGraph == nullptr)
        return nullptr;

    auto node = audioGraph->getNodeForId(nodeId);
    if (node == nullptr)
        return nullptr;

    return node->getProcessor();
}

juce::AudioProcessorGraph::NodeID ModuleSystemComponent::addProcessor(juce::AudioProcessor* processor)
{
    if (audioGraph == nullptr || processor == nullptr)
        return {};

    auto node = audioGraph->addNode(std::unique_ptr<juce::AudioProcessor>(processor));
    if (node != nullptr)
    {
        // DBG("Added processor node with ID: " + juce::String(node->nodeID.uid));
        return node->nodeID;
    }

    return {};
}

bool ModuleSystemComponent::removeProcessor(juce::AudioProcessorGraph::NodeID nodeId)
{
    if (audioGraph == nullptr)
        return false;

    disconnectProcessor(nodeId);
    return audioGraph->removeNode(nodeId);
}

void ModuleSystemComponent::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    currentSampleRate = sampleRate;
    currentBlockSize = samplesPerBlock;

    if (audioGraph != nullptr)
    {
        audioGraph->setPlayConfigDetails(2, 2, sampleRate, samplesPerBlock);
        audioGraph->prepareToPlay(sampleRate, samplesPerBlock);
    }
}

void ModuleSystemComponent::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    // DBG("ModuleSystemComponent: processBlock called");
    if (audioGraph != nullptr)
    {
        audioGraph->processBlock(buffer, midiMessages);
    }
}

void ModuleSystemComponent::releaseResources()
{
    DBG("ModuleSystemComponent::releaseResources - Releasing all resources");

    // 移除所有子组件
    removeAllChildren();

    if (audioGraph != nullptr)
    {
        // 1. 停止所有处理
        audioGraph->suspendProcessing(true);

        // 2. 移除所有连接
        // 替换为对每个节点调用disconnectNode
        for (auto node : audioGraph->getNodes())
        {
            audioGraph->disconnectNode(node->nodeID);
        }
        DBG("Cleared all audio graph connections");

        // 3. 按逆序释放节点（确保依赖关系正确处理）
        auto graphNodes = audioGraph->getNodes();
        for (int i = graphNodes.size() - 1; i >= 0; --i)
        {
            if (auto node = graphNodes[i])
            {
                if (auto* processor = node->getProcessor())
                {
                    // 特殊处理某些处理器类型
                    if (auto* midiInputProcessor = dynamic_cast<MidiInputProcessor*>(processor))
                    {
                        // 确保MIDI输入处理器正确释放资源
                        midiInputProcessor->disableMidiInputs();
                    }

                    // 调用处理器的releaseResources方法
                    processor->releaseResources();
                }

                // 从图中移除节点
                audioGraph->removeNode(node->nodeID);
            }
        }
        DBG("Removed all audio graph nodes");

        // 4. 释放音频图资源
        audioGraph->releaseResources();

        // 5. 清空节点列表
        nodes.clear();

        DBG("Audio graph resources released");
    }

    // 确保设备管理器引用被清除
    deviceManager = nullptr;
}

void ModuleSystemComponent::printConnections() const
{
    if (audioGraph == nullptr)
        return;

    auto connections = audioGraph->getConnections();
    if (connections.empty())
    {
        DBG("  No connections");
        return;
    }

    for (const auto& connection : connections)
    {
        juce::String connectionType;
        if (connection.source.channelIndex == juce::AudioProcessorGraph::midiChannelIndex)
            connectionType = "MIDI";
        else
            connectionType = "Audio";

        auto* sourceProc = getProcessorForNode(connection.source.nodeID);
        auto* destProc = getProcessorForNode(connection.destination.nodeID);

        juce::String sourceInfo = sourceProc != nullptr ? sourceProc->getName() : "Unknown";
        juce::String destInfo = destProc != nullptr ? destProc->getName() : "Unknown";

        DBG("  - " + connectionType + " Connection:");
        DBG("    Source Node: ID=" + juce::String(connection.source.nodeID.uid) +
            " (" + sourceInfo + "), Channel=" + juce::String(connection.source.channelIndex));
        DBG("    Destination Node: ID=" + juce::String(connection.destination.nodeID.uid) +
            " (" + destInfo + "), Channel=" + juce::String(connection.destination.channelIndex));
    }
}

void ModuleSystemComponent::createProcessors()
{
    // 移除自动创建的代码
}

void ModuleSystemComponent::prepareAll()
{
    for (const auto& node : nodes)
    {
        if (node->processor != nullptr)
        {
            node->processor->prepareToPlay(currentSampleRate, currentBlockSize);
        }
    }
}

void ModuleSystemComponent::connectProcessors()
{
    // 移除自动连接的代码
}

// 获取所有当前连接
std::vector<juce::AudioProcessorGraph::Connection> ModuleSystemComponent::getConnections() const
{
    if (audioGraph == nullptr)
        return {}; // 如果音频图不存在，返回空列表

    return audioGraph->getConnections();
}

void ModuleSystemComponent::updateAudioSettings(double sampleRate, int samplesPerBlock)
{
    DBG("ModuleSystemComponent::updateAudioSettings: Updating audio settings to " +
        juce::String(sampleRate) + "Hz, " + juce::String(samplesPerBlock) + "samples");

    currentSampleRate = sampleRate;
    currentBlockSize = samplesPerBlock;

    if (audioGraph != nullptr)
    {
        // Suspend processing
        audioGraph->suspendProcessing(true);

        // 保存当前MIDI连接信息，以便稍后恢复
        struct MidiConnectionInfo {
            juce::AudioProcessorGraph::NodeID sourceNodeId;
            juce::AudioProcessorGraph::NodeID destNodeId;
        };
        std::vector<MidiConnectionInfo> midiConnections;

        // 查找所有MIDI连接
        for (auto& connection : audioGraph->getConnections())
        {
            if (connection.source.channelIndex == juce::AudioProcessorGraph::midiChannelIndex &&
                connection.destination.channelIndex == juce::AudioProcessorGraph::midiChannelIndex)
            {
                midiConnections.push_back({connection.source.nodeID, connection.destination.nodeID});
            }
        }

        // Update audio settings
        audioGraph->setPlayConfigDetails(
            deviceManager != nullptr ? deviceManager->getCurrentAudioDevice()->getActiveInputChannels().countNumberOfSetBits() : 2,
            deviceManager != nullptr ? deviceManager->getCurrentAudioDevice()->getActiveOutputChannels().countNumberOfSetBits() : 2,
            sampleRate,
            samplesPerBlock);

        // Notify all processors to prepare for playback
        for (auto* node : audioGraph->getNodes())
        {
            if (node->getProcessor() != nullptr)
            {
                node->getProcessor()->setPlayConfigDetails(
                    node->getProcessor()->getTotalNumInputChannels(),
                    node->getProcessor()->getTotalNumOutputChannels(),
                    sampleRate,
                    samplesPerBlock);

                node->getProcessor()->prepareToPlay(sampleRate, samplesPerBlock);
            }
        }

        // 重新初始化MIDI连接状态
        for (const auto& connection : midiConnections)
        {
            auto* destProcessor = getProcessorForNode(connection.destNodeId);
            if (destProcessor != nullptr)
            {
                // 为OscillatorProcessor重新启用MIDI输入
                if (auto* oscProcessor = dynamic_cast<OscillatorProcessor*>(destProcessor))
                {
                    oscProcessor->enableMidiInput(true);
                    oscProcessor->setMidiInputNodeId(connection.sourceNodeId);
                    DBG("Re-enabled MIDI input for oscillator node " + juce::String(connection.destNodeId.uid));
                }

                // 为EnvelopeGeneratorProcessor重新启用MIDI输入
                if (auto* envProcessor = dynamic_cast<EnvelopeGeneratorProcessor*>(destProcessor))
                {
                    envProcessor->enableMidiInput(true);
                    envProcessor->setMidiInputNodeId(connection.sourceNodeId);
                    DBG("Re-enabled MIDI input for envelope generator node " + juce::String(connection.destNodeId.uid));
                }
            }

            // 确保MidiInputProcessor正确初始化
            auto* sourceProcessor = getProcessorForNode(connection.sourceNodeId);
            if (sourceProcessor != nullptr)
            {
                if (auto* midiInputProcessor = dynamic_cast<MidiInputProcessor*>(sourceProcessor))
                {
                    // 强制刷新MIDI输入设备
                    midiInputProcessor->refreshMidiInputs();
                    DBG("Refreshed MIDI inputs for MidiInputProcessor node " + juce::String(connection.sourceNodeId.uid));
                }
            }
        }

        // Prepare audio graph
        audioGraph->prepareToPlay(sampleRate, samplesPerBlock);

        // Resume processing
        audioGraph->suspendProcessing(false);

        DBG("Audio settings update completed");
    }
}