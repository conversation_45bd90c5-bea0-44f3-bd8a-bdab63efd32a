#include "MixerProcessor.h"
#include "MixerProcessorComponent.h"
#include <cmath>

MixerProcessor::MixerProcessor()
    : AudioProcessor(BusesProperties()
        // One stereo output bus
        .withOutput("Output", juce::AudioChannelSet::stereo(), true)
        // Four stereo input buses
        .withInput("Input 1", juce::AudioChannelSet::stereo(), true)
        .withInput("Input 2", juce::AudioChannelSet::stereo(), true)
        .withInput("Input 3", juce::AudioChannelSet::stereo(), true)
        .withInput("Input 4", juce::AudioChannelSet::stereo(), true)),
    parameters(*this, nullptr, "MixerParams", createParameterLayout()),
    // Initialize all atomic variables
    masterVolume(0.8f),
    levelDecayRate(0.9975f)
{
    // Initialize all channel volumes, pans, and mutes
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        volumes[i] = 0.8f;
        pans[i] = 0.0f;
        mutes[i] = false;
        inputLevels[i][0] = 0.0f;
        inputLevels[i][1] = 0.0f;
    }
    
    // Initialize output levels
    outputLevels[0] = 0.0f;
    outputLevels[1] = 0.0f;
    
    // Register parameters for listening
    DBG("MixerProcessor: Setting up parameter listeners");
    parameters.addParameterListener("masterVolume", this);
    
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        juce::String volParam = "volume" + juce::String(i);
        juce::String panParam = "pan" + juce::String(i);
        juce::String muteParam = "mute" + juce::String(i);
        
        DBG("MixerProcessor: Adding listener for " + volParam);
        parameters.addParameterListener(volParam, this);
        
        DBG("MixerProcessor: Adding listener for " + panParam);
        parameters.addParameterListener(panParam, this);
        
        DBG("MixerProcessor: Adding listener for " + muteParam);
        parameters.addParameterListener(muteParam, this);
    }
    
    DBG("MixerProcessor: Construction complete");
}

MixerProcessor::~MixerProcessor()
{
    // Remove all parameter listeners
    parameters.removeParameterListener("masterVolume", this);
    
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        parameters.removeParameterListener("volume" + juce::String(i), this);
        parameters.removeParameterListener("pan" + juce::String(i), this);
        parameters.removeParameterListener("mute" + juce::String(i), this);
    }
}

void MixerProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    // Update level meter decay rate based on sample rate
    levelDecayRate = 1.0f - (float)(1.0 / (0.2 * sampleRate)); // ~200ms decay time
    
    // Allocate mix buffer
    mixBuffer.setSize(2, samplesPerBlock);
}

void MixerProcessor::releaseResources()
{
    // Nothing specific to release here
}

void MixerProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    // Clear any input MIDI messages
    midiMessages.clear();

    // Instead of clearing the input buffer, create a copy for processing
    // Create a temporary buffer to store the input
    juce::AudioBuffer<float> tempBuffer;
    tempBuffer.makeCopyOf(buffer);
    
    // Clear mix buffer for our own processing
    mixBuffer.clear();
    
    // Process each input channel
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        // Don't process if bus is not enabled
        if (!getBus(true, i)->isEnabled())
            continue;
            
        // Get input buffer for this channel
        auto inputBuffer = getBusBuffer(tempBuffer, true, i);
        
        // Skip processing if muted, but still update level meters to decay
        if (mutes[i].load())
        {
            float currentLevel0 = inputLevels[i][0].load();
            float currentLevel1 = inputLevels[i][1].load();
            inputLevels[i][0] = currentLevel0 * levelDecayRate;
            inputLevels[i][1] = currentLevel1 * levelDecayRate;
            
            // Important: We don't add any signal from this channel to the mix buffer
            // but we still continue processing other channels
            continue;
        }
        
        // Calculate pan values based on the current pan setting
        // For stereo channels, we need to determine how much of each side goes to the left/right output
        float panValue = pans[i].load();
        float leftGain = (panValue <= 0.0f) ? 1.0f : 1.0f - panValue;
        float rightGain = (panValue >= 0.0f) ? 1.0f : 1.0f + panValue;
        
        // Apply volume
        float volValue = volumes[i].load();
        
        // If stereo input (which should be the case)
        if (inputBuffer.getNumChannels() >= 2)
        {
            // Process left and right with panning
            const float* leftChannel = inputBuffer.getReadPointer(0);
            const float* rightChannel = inputBuffer.getReadPointer(1);
            
            float* mixLeft = mixBuffer.getWritePointer(0);
            float* mixRight = mixBuffer.getWritePointer(1);
            
            // Add to mix buffer with panning
            for (int j = 0; j < buffer.getNumSamples(); ++j)
            {
                // Calculate panned contributions
                float leftOut = leftChannel[j] * leftGain * volValue;
                float rightOut = rightChannel[j] * rightGain * volValue;
                
                // Add to mix
                mixLeft[j] += leftOut;
                mixRight[j] += rightOut;
                
                // Update level meters - using simple peak detection
                float currentLevel0 = inputLevels[i][0].load();
                float currentLevel1 = inputLevels[i][1].load();
                float absLeftOut = std::abs(leftOut);
                float absRightOut = std::abs(rightOut);
                float decayedLevel0 = currentLevel0 * levelDecayRate;
                float decayedLevel1 = currentLevel1 * levelDecayRate;
                
                inputLevels[i][0] = (decayedLevel0 > absLeftOut) ? decayedLevel0 : absLeftOut;
                inputLevels[i][1] = (decayedLevel1 > absRightOut) ? decayedLevel1 : absRightOut;
            }
        }
        else if (inputBuffer.getNumChannels() == 1)
        {
            // Mono input handling (fallback, shouldn't be reached based on bus validation)
            const float* monoChannel = inputBuffer.getReadPointer(0);
            
            float* mixLeft = mixBuffer.getWritePointer(0);
            float* mixRight = mixBuffer.getWritePointer(1);
            
            // Add to mix buffer with panning
            for (int j = 0; j < buffer.getNumSamples(); ++j)
            {
                // Calculate panned contributions
                float value = monoChannel[j] * volValue;
                float leftOut = value * leftGain;
                float rightOut = value * rightGain;
                
                // Add to mix
                mixLeft[j] += leftOut;
                mixRight[j] += rightOut;
                
                // Update level meters
                float currentLevel0 = inputLevels[i][0].load();
                float currentLevel1 = inputLevels[i][1].load();
                float absLeftOut = std::abs(leftOut);
                float absRightOut = std::abs(rightOut);
                float decayedLevel0 = currentLevel0 * levelDecayRate;
                float decayedLevel1 = currentLevel1 * levelDecayRate;
                
                inputLevels[i][0] = (decayedLevel0 > absLeftOut) ? decayedLevel0 : absLeftOut;
                inputLevels[i][1] = (decayedLevel1 > absRightOut) ? decayedLevel1 : absRightOut;
            }
        }
    }
    
    // Now apply master volume to the mix buffer and copy to output buffer
    float masterVol = masterVolume.load();
    
    float* mixLeft = mixBuffer.getWritePointer(0);
    float* mixRight = mixBuffer.getWritePointer(1);
    
    // Now write the mixed audio to the original buffer
    // Clear output buffer first to ensure we start with silence
    buffer.clear();
    
    float* outputLeft = buffer.getWritePointer(0);
    float* outputRight = buffer.getWritePointer(1);
    
    for (int j = 0; j < buffer.getNumSamples(); ++j)
    {
        // Apply master volume
        float leftOut = mixLeft[j] * masterVol;
        float rightOut = mixRight[j] * masterVol;
        
        // Write the processed audio to the output buffer
        outputLeft[j] = leftOut;
        outputRight[j] = rightOut;
        
        // Update output level meters
        float currentOutLevel0 = outputLevels[0].load();
        float currentOutLevel1 = outputLevels[1].load();
        float absLeftOut = std::abs(leftOut);
        float absRightOut = std::abs(rightOut);
        float decayedLevel0 = currentOutLevel0 * levelDecayRate;
        float decayedLevel1 = currentOutLevel1 * levelDecayRate;
        
        outputLevels[0] = (decayedLevel0 > absLeftOut) ? decayedLevel0 : absLeftOut;
        outputLevels[1] = (decayedLevel1 > absRightOut) ? decayedLevel1 : absRightOut;
    }
}

void MixerProcessor::parameterChanged(const juce::String& parameterID, float newValue)
{
    // Log every parameter change
    DBG("MixerProcessor::parameterChanged - Parameter: " + parameterID + ", Value: " + juce::String(newValue));
    
    // Get the raw parameter from the parameter tree to validate the value
    auto param = parameters.getParameter(parameterID);
    if (param != nullptr)
    {
        DBG("MixerProcessor::parameterChanged - Parameter exists: " + parameterID + 
            ", Default value: " + juce::String(param->getDefaultValue()) + 
            ", Current value: " + juce::String(param->getValue()));
    }
    else
    {
        DBG("MixerProcessor::parameterChanged - ERROR: Parameter not found: " + parameterID);
    }
    
    // Handle master volume change
    if (parameterID == "masterVolume")
    {
        masterVolume = newValue;
        DBG("Master volume changed to: " + juce::String(newValue));
    }
    // Handle channel parameter changes
    else
    {
        // Check if this is a volume parameter
        if (parameterID.startsWith("volume"))
        {
            // Extract channel number
            int channelIndex = parameterID.substring(6).getIntValue();
            if (channelIndex >= 0 && channelIndex < NUM_CHANNELS)
            {
                volumes[channelIndex] = newValue;
                DBG("Channel " + juce::String(channelIndex) + " volume changed to: " + juce::String(newValue));
            }
        }
        // Check if this is a pan parameter
        else if (parameterID.startsWith("pan"))
        {
            // Extract channel number
            int channelIndex = parameterID.substring(3).getIntValue();
            if (channelIndex >= 0 && channelIndex < NUM_CHANNELS)
            {
                pans[channelIndex] = newValue;
                DBG("Channel " + juce::String(channelIndex) + " pan changed to: " + juce::String(newValue));
            }
        }
        // Check if this is a mute parameter
        else if (parameterID.startsWith("mute"))
        {
            // Extract channel number
            int channelIndex = parameterID.substring(4).getIntValue();
            if (channelIndex >= 0 && channelIndex < NUM_CHANNELS)
            {
                bool isMuted = newValue > 0.5f;
                
                // Check if there's an actual state change
                bool oldState = mutes[channelIndex].load();
                if (oldState != isMuted)
                {
                    DBG("Channel " + juce::String(channelIndex) + " mute state CHANGED from: " + 
                        juce::String(oldState ? "muted" : "unmuted") + " to: " + 
                        juce::String(isMuted ? "muted" : "unmuted") + " (value=" + juce::String(newValue) + ")");
                }
                else
                {
                    DBG("Channel " + juce::String(channelIndex) + " mute state UNCHANGED: " + 
                        juce::String(isMuted ? "muted" : "unmuted") + " (value=" + juce::String(newValue) + ")");
                }
                
                // Update the cached atomic value
                mutes[channelIndex] = isMuted;
            }
        }
        else
        {
            // Unknown parameter
            DBG("Unknown parameter changed: " + parameterID);
        }
    }
}

float MixerProcessor::getInputLevel(int channel, int lr) const
{
    if (channel >= 0 && channel < NUM_CHANNELS && lr >= 0 && lr < 2)
        return inputLevels[channel][lr].load();
    return 0.0f;
}

float MixerProcessor::getOutputLevel(int lr) const
{
    if (lr >= 0 && lr < 2)
        return outputLevels[lr].load();
    return 0.0f;
}

void MixerProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    // Store parameters
    auto state = parameters.copyState();
    juce::ValueTree tree = state;
    juce::MemoryOutputStream stream(destData, false);
    tree.writeToStream(stream);
}

void MixerProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    // Restore parameters
    juce::ValueTree tree = juce::ValueTree::readFromData(data, static_cast<size_t>(sizeInBytes));
    if (tree.isValid())
        parameters.replaceState(tree);
}

// Override createEditor to return the custom component
juce::AudioProcessorEditor* MixerProcessor::createEditor()
{
    return new MixerProcessorComponent(*this);
}

bool MixerProcessor::hasEditor() const
{
    return true;
} 