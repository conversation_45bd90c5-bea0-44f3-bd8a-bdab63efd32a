#include "MixerProcessor.h"
#include "MixerProcessorComponent.h"
#include <cmath>

MixerProcessor::MixerProcessor()
    : AudioProcessor(BusesProperties()
        .withOutput("Output", juce::AudioChannelSet::stereo(), true)
        .withInput("Input 1", juce::AudioChannelSet::stereo(), true)
        .withInput("Input 2", juce::AudioChannelSet::stereo(), true)
        .withInput("Input 3", juce::AudioChannelSet::stereo(), true)
        .withInput("Input 4", juce::AudioChannelSet::stereo(), true)),
    parameters(*this, nullptr, "MixerParams", createParameterLayout()),
    smoothedMasterVolume(0.8f),
    levelDecayRate(0.9975f)
{
    // Initialize smoothed values
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        smoothedVolumes[i].setCurrentAndTargetValue(0.8f);
        smoothedPans[i].setCurrentAndTargetValue(0.0f);
        mutes[i] = false;
        inputLevels[i][0] = 0.0f;
        inputLevels[i][1] = 0.0f;
    }
    outputLevels[0] = 0.0f;
    outputLevels[1] = 0.0f;

    // Register parameter listeners
    parameters.addParameterListener("masterVolume", this);
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        parameters.addParameterListener("volume" + juce::String(i), this);
        parameters.addParameterListener("pan" + juce::String(i), this);
        parameters.addParameterListener("mute" + juce::String(i), this);
    }
}

MixerProcessor::~MixerProcessor()
{
    parameters.removeParameterListener("masterVolume", this);
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        parameters.removeParameterListener("volume" + juce::String(i), this);
        parameters.removeParameterListener("pan" + juce::String(i), this);
        parameters.removeParameterListener("mute" + juce::String(i), this);
    }
}

void MixerProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    levelDecayRate = std::exp(-1.0f / (0.2f * static_cast<float>(sampleRate))); // ~200ms decay time

    mixBuffer.setSize(2, samplesPerBlock);

    juce::dsp::ProcessSpec spec { sampleRate, static_cast<juce::uint32>(samplesPerBlock), 2 };

    masterGain.prepare(spec);
    masterGain.setGainLinear(smoothedMasterVolume.getTargetValue());

    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        channelGains[i].prepare(spec);
        channelGains[i].setGainLinear(smoothedVolumes[i].getTargetValue());
        panners[i].prepare(spec);
        panners[i].setPan(smoothedPans[i].getTargetValue());
        smoothedVolumes[i].reset(sampleRate, 0.05); // 50ms smoothing
        smoothedPans[i].reset(sampleRate, 0.05);
    }
    smoothedMasterVolume.reset(sampleRate, 0.05);
}

void MixerProcessor::releaseResources()
{
    mixBuffer.setSize(0, 0);
}

void MixerProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    midiMessages.clear();
    juce::ScopedNoDenormals noDenormals;

    auto numSamples = buffer.getNumSamples();
    auto mainOutputBlock = juce::dsp::AudioBlock<float>(buffer);
    mainOutputBlock.clear();

    // Update smoothed parameters
    smoothedMasterVolume.setTargetValue(*parameters.getRawParameterValue("masterVolume"));
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        smoothedVolumes[i].setTargetValue(*parameters.getRawParameterValue("volume" + juce::String(i)));
        smoothedPans[i].setTargetValue(*parameters.getRawParameterValue("pan" + juce::String(i)));
        mutes[i] = *parameters.getRawParameterValue("mute" + juce::String(i)) > 0.5f;
    }

    // Process each input bus
    for (int i = 0; i < NUM_CHANNELS; ++i)
    {
        // Check if the input bus is enabled
        if (!getBus(true, i)->isEnabled())
        {
            // If bus is not enabled, decay its level meters and skip processing
            inputLevels[i][0] = inputLevels[i][0].load() * levelDecayRate;
            inputLevels[i][1] = inputLevels[i][1].load() * levelDecayRate;
            continue;
        }

        // Get the input buffer for the current bus
        auto inputBusBuffer = getBusBuffer(buffer, true, i);

        // Create a temporary AudioBlock for processing this input bus
        // This ensures we are not processing in-place on the main buffer
        juce::AudioBuffer<float> tempInputBuffer (inputBusBuffer.getNumChannels(), numSamples);
        tempInputBuffer.copyFrom(0, 0, inputBusBuffer, 0, 0, numSamples);
        if (inputBusBuffer.getNumChannels() > 1)
            tempInputBuffer.copyFrom(1, 0, inputBusBuffer, 1, 0, numSamples);

        auto inputBlock = juce::dsp::AudioBlock<float>(tempInputBuffer);
        juce::dsp::ProcessContextReplacing<float> context(inputBlock);

        // Update input level meter before processing - calculate magnitude manually
        float leftMag = 0.0f, rightMag = 0.0f;
        if (inputBlock.getNumChannels() > 0)
        {
            auto leftChannel = inputBlock.getChannelPointer(0);
            for (size_t sample = 0; sample < inputBlock.getNumSamples(); ++sample)
                leftMag = juce::jmax(leftMag, std::abs(leftChannel[sample]));
        }
        if (inputBlock.getNumChannels() > 1)
        {
            auto rightChannel = inputBlock.getChannelPointer(1);
            for (size_t sample = 0; sample < inputBlock.getNumSamples(); ++sample)
                rightMag = juce::jmax(rightMag, std::abs(rightChannel[sample]));
        }

        inputLevels[i][0] = juce::jmax(inputLevels[i][0].load() * levelDecayRate, leftMag);
        inputLevels[i][1] = juce::jmax(inputLevels[i][1].load() * levelDecayRate, rightMag);

        if (mutes[i].load())
        {
            inputBlock.clear();
            // No need to add to mainOutputBlock if muted and cleared
            continue;
        }

        // Apply channel gain and pan
        channelGains[i].setGainLinear(smoothedVolumes[i].getNextValue());
        channelGains[i].process(context);

        panners[i].setPan(smoothedPans[i].getNextValue());
        panners[i].process(context);

        // Add the processed inputBlock to the mainOutputBlock
        mainOutputBlock.add(inputBlock);
    }

    // Apply master gain
    juce::dsp::ProcessContextReplacing<float> masterContext(mainOutputBlock);
    masterGain.setGainLinear(smoothedMasterVolume.getNextValue());
    masterGain.process(masterContext);

    // Update output level meter - calculate magnitude manually
    float outputLeftMag = 0.0f, outputRightMag = 0.0f;
    if (buffer.getNumChannels() > 0)
    {
        auto leftChannel = buffer.getReadPointer(0);
        for (int sample = 0; sample < numSamples; ++sample)
            outputLeftMag = juce::jmax(outputLeftMag, std::abs(leftChannel[sample]));
    }
    if (buffer.getNumChannels() > 1)
    {
        auto rightChannel = buffer.getReadPointer(1);
        for (int sample = 0; sample < numSamples; ++sample)
            outputRightMag = juce::jmax(outputRightMag, std::abs(rightChannel[sample]));
    }

    outputLevels[0] = juce::jmax(outputLevels[0].load() * levelDecayRate, outputLeftMag);
    outputLevels[1] = juce::jmax(outputLevels[1].load() * levelDecayRate, outputRightMag);
}

void MixerProcessor::parameterChanged(const juce::String& parameterID, float newValue)
{
    // This callback is now only for setting the target values of smoothed parameters.
    // The actual DSP update happens in processBlock.
    if (parameterID.startsWith("volume"))
    {
        int channelIndex = parameterID.substring(6).getIntValue();
        if (channelIndex >= 0 && channelIndex < NUM_CHANNELS)
            smoothedVolumes[channelIndex].setTargetValue(newValue);
    }
    else if (parameterID.startsWith("pan"))
    {
        int channelIndex = parameterID.substring(3).getIntValue();
        if (channelIndex >= 0 && channelIndex < NUM_CHANNELS)
            smoothedPans[channelIndex].setTargetValue(newValue);
    }
    else if (parameterID == "masterVolume")
    {
        smoothedMasterVolume.setTargetValue(newValue);
    }
    else if (parameterID.startsWith("mute"))
    {
        int channelIndex = parameterID.substring(4).getIntValue();
        if (channelIndex >= 0 && channelIndex < NUM_CHANNELS)
            mutes[channelIndex] = newValue > 0.5f;
    }
}

float MixerProcessor::getInputLevel(int channel, int lr) const
{
    if (channel >= 0 && channel < NUM_CHANNELS && lr >= 0 && lr < 2)
        return inputLevels[channel][lr].load();
    return 0.0f;
}

float MixerProcessor::getOutputLevel(int lr) const
{
    if (lr >= 0 && lr < 2)
        return outputLevels[lr].load();
    return 0.0f;
}

void MixerProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    auto state = parameters.copyState();
    std::unique_ptr<juce::XmlElement> xml(state.createXml());
    copyXmlToBinary(*xml, destData);
}

void MixerProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    std::unique_ptr<juce::XmlElement> xmlState(getXmlFromBinary(data, sizeInBytes));
    if (xmlState.get() != nullptr)
        parameters.replaceState(juce::ValueTree::fromXml(*xmlState));
}

juce::AudioProcessorEditor* MixerProcessor::createEditor()
{
    return new MixerProcessorComponent(*this);
}

bool MixerProcessor::hasEditor() const
{
    return true;
}