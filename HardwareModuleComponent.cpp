#include "HardwareModuleComponent.h"
#include "ProjectManager.h"

// Linux系统相关头文件
#if JUCE_LINUX
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#endif

HardwareModuleComponent::HardwareModuleComponent()
    : moduleManager(std::make_unique<ModuleManager>(deviceManager))
{
    // 配置音频设备
    deviceManager.initialiseWithDefaultDevices(2, 2);

    // 初始化ModuleManager
    moduleManager->initialize();

    // 创建项目管理器
    projectManager = std::make_unique<ProjectManager>(*moduleManager);

    // 设置保存按钮
    addAndMakeVisible(saveButton);
    saveButton.setButtonText("Save Project");
    saveButton.setEnabled(true); // 确保按钮启用
    saveButton.onClick = [this]
    {
        saveProject();
    };

    // 设置加载按钮
    addAndMakeVisible(loadButton);
    loadButton.setButtonText("Load Project");
    loadButton.setEnabled(true); // 确保按钮启用
    loadButton.onClick = [this]
    {
        loadProject();
    };

    // 设置定时器用于定期检查CAN总线
    startTimer(50); // 每50毫秒检查一次

    // 初始化CAN接口
    initializeCanInterface();

    setSize(800, 600);
}

HardwareModuleComponent::~HardwareModuleComponent()
{
    stopTimer();

    // 关闭CAN接口
    #if JUCE_LINUX
    if (canSocket >= 0)
    {
        close(canSocket);
        canSocket = -1;
    }
    #endif

    canInterfaceActive = false;
}

void HardwareModuleComponent::paint(juce::Graphics& g)
{
    g.fillAll(getLookAndFeel().findColour(juce::ResizableWindow::backgroundColourId));

    // 显示连接状态
    g.setColour(juce::Colours::white);
    g.setFont(15.0f);

    juce::String statusText = canInterfaceActive ?
        "CAN Interface: Connected" : "CAN Interface: Disconnected";
    g.drawText(statusText, getLocalBounds(), juce::Justification::topLeft, true);

    // 显示已连接的模块信息
    g.setFont(12.0f);
    int y = 30;

    for (const auto& [moduleType, instances] : moduleTypeToInstances)
    {
        g.drawText(moduleType + ": " + juce::String(instances.size()) + " instances",
                  10, y, getWidth() - 20, 20, juce::Justification::left, true);
        y += 20;
    }
}

void HardwareModuleComponent::resized()
{
    auto area = getLocalBounds();

    // 顶部区域用于按钮
    auto topArea = area.removeFromTop(50);

    // 设置按钮位置
    saveButton.setBounds(topArea.removeFromLeft(120).reduced(10));
    loadButton.setBounds(topArea.removeFromLeft(120).reduced(10));

    // 确保按钮在前面
    saveButton.toFront(false);
    loadButton.toFront(false);
}

bool HardwareModuleComponent::initializeCanInterface(const juce::String& canInterface)
{
    #if JUCE_LINUX
    // 创建CAN套接字
    canSocket = socket(PF_CAN, SOCK_RAW, CAN_RAW);
    if (canSocket < 0)
    {
        DBG("Error creating CAN socket");
        return false;
    }

    // 指定CAN接口
    struct ifreq ifr;
    std::strcpy(ifr.ifr_name, canInterface.toRawUTF8());
    if (ioctl(canSocket, SIOCGIFINDEX, &ifr) < 0)
    {
        DBG("Error getting CAN interface index");
        close(canSocket);
        canSocket = -1;
        return false;
    }

    // 绑定套接字到CAN接口
    struct sockaddr_can addr;
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;

    if (bind(canSocket, (struct sockaddr*)&addr, sizeof(addr)) < 0)
    {
        DBG("Error binding CAN socket");
        close(canSocket);
        canSocket = -1;
        return false;
    }

    // 设置为非阻塞模式
    int flags = fcntl(canSocket, F_GETFL, 0);
    fcntl(canSocket, F_SETFL, flags | O_NONBLOCK);

    canInterfaceActive = true;
    DBG("CAN interface initialized successfully");
    return true;
    #else
    // 在非Linux平台上，模拟CAN接口连接成功
    DBG("CAN interface simulated in non-Linux platform");
    canInterfaceActive = true;
    return true;
    #endif
}

void HardwareModuleComponent::timerCallback()
{
    if (!canInterfaceActive)
        return;

    #if JUCE_LINUX
    // 读取CAN消息
    struct can_frame frame;
    ssize_t nbytes = read(canSocket, &frame, sizeof(struct can_frame));

    // 如果收到数据
    if (nbytes > 0)
    {
        // 解析CAN帧数据到我们自己的消息结构
        CanMessage message;

        // 假设CAN数据格式如下:
        // [0]: 模块类型
        // [1]: 模块索引
        // [2]: 参数索引
        // [3]: 参数值
        // [4-7]: 附加数据

        if (frame.can_dlc >= 4) // 至少需要4个字节
        {
            message.moduleType = frame.data[0];
            message.moduleIndex = frame.data[1];
            message.paramIndex = frame.data[2];
            message.paramValue = frame.data[3];

            // 复制任何附加数据
            for (int i = 4; i < frame.can_dlc && i < 8; ++i)
            {
                message.additionalData[i-4] = frame.data[i];
            }

            // 处理消息
            handleCanMessage(message);
        }
    }
    #endif

    // 在非Linux环境中可以模拟接收CAN消息进行测试
}

void HardwareModuleComponent::handleCanMessage(const CanMessage& message)
{
    // 根据消息类型和内容执行不同操作

    // 案例1：消息表示一个新模块连接
    // 假设当参数索引和值都为0时，表示新模块请求连接
    if (message.paramIndex == 0 && message.paramValue == 0)
    {
        DBG("New module of type " + juce::String(message.moduleType) + " connected");

        // 创建新模块
        createModuleFromCanMessage(message.moduleType);

        // 需要发送初始化参数给新模块
        sendModuleInitParams(message.moduleType, message.moduleIndex);
    }
    // 案例2：现有模块的参数更新
    else
    {
        DBG("Parameter update: Module type=" + juce::String(message.moduleType) +
           ", Index=" + juce::String(message.moduleIndex) +
           ", Param=" + juce::String(message.paramIndex) +
           ", Value=" + juce::String(message.paramValue));

        // 更新模块参数
        updateModuleParameter(message.moduleType, message.moduleIndex,
                             message.paramIndex, message.paramValue);
    }

    // 触发重绘
    repaint();
}

ModuleUI* HardwareModuleComponent::findModuleByTypeAndIndex(uint8_t moduleType, uint8_t moduleIndex)
{
    juce::String typeString = getModuleTypeString(moduleType);

    if (typeString.isEmpty())
        return nullptr;

    // 检查映射表中是否有这种类型的模块
    auto it = moduleTypeToInstances.find(typeString);
    if (it == moduleTypeToInstances.end() || moduleIndex >= it->second.size())
        return nullptr;

    // 获取模块ID
    auto nodeId = it->second[moduleIndex];

    // 在moduleUIs中查找这个ID的模块
    for (auto& moduleUI : moduleManager->getModuleUIs())
    {
        if (moduleUI->nodeId == nodeId)
            return moduleUI.get();
    }

    return nullptr;
}

bool HardwareModuleComponent::sendCanMessage(const CanMessage& message)
{
    #if JUCE_LINUX
    if (!canInterfaceActive || canSocket < 0)
        return false;

    struct can_frame frame;
    memset(&frame, 0, sizeof(frame));

    // 设置数据长度
    frame.can_dlc = 8; // 使用全部8个字节

    // 填充数据
    frame.data[0] = message.moduleType;
    frame.data[1] = message.moduleIndex;
    frame.data[2] = message.paramIndex;
    frame.data[3] = message.paramValue;

    // 复制附加数据
    for (int i = 0; i < 4; ++i)
    {
        frame.data[i+4] = message.additionalData[i];
    }

    // 发送帧
    ssize_t nbytes = write(canSocket, &frame, sizeof(struct can_frame));

    return (nbytes == sizeof(struct can_frame));
    #else
    // 在非Linux环境中模拟发送成功
    DBG("Simulating CAN message send: Type=" + juce::String(message.moduleType) +
       ", Index=" + juce::String(message.moduleIndex) +
       ", Param=" + juce::String(message.paramIndex) +
       ", Value=" + juce::String(message.paramValue));
    return true;
    #endif
}

void HardwareModuleComponent::createModuleFromCanMessage(uint8_t moduleType)
{
    // 将模块类型转换为字符串
    juce::String typeString = getModuleTypeString(moduleType);

    if (typeString.isEmpty())
    {
        DBG("Unknown module type: " + juce::String(moduleType));
        return;
    }

    // 创建新模块
    DBG("Creating module of type: " + typeString);
    moduleManager->createModule(typeString, juce::String());

    // 获取新创建的模块ID
    const auto& moduleUIs = moduleManager->getModuleUIs();
    if (!moduleUIs.empty())
    {
        auto& newModule = moduleUIs.back();

        // 将模块ID添加到映射表中
        moduleTypeToInstances[typeString].push_back(newModule->nodeId);

        DBG("Module created with ID: " + juce::String(newModule->nodeId.uid));
    }
}

void HardwareModuleComponent::sendModuleInitParams(uint8_t moduleType, uint8_t moduleIndex)
{
    // 查找指定类型和索引的模块
    ModuleUI* moduleUI = findModuleByTypeAndIndex(moduleType, moduleIndex);
    if (!moduleUI)
    {
        DBG("Cannot find module to initialize. Type: " + juce::String(moduleType) +
           ", Index: " + juce::String(moduleIndex));
        return;
    }

    // 创建初始化消息
    CanMessage message;
    message.moduleType = moduleType;
    message.moduleIndex = moduleIndex;

    // 假设我们要发送三个参数
    // 参数1初始化
    message.paramIndex = 0;
    message.paramValue = getModuleParameterValue(moduleUI, 0);
    sendCanMessage(message);

    // 参数2初始化
    message.paramIndex = 1;
    message.paramValue = getModuleParameterValue(moduleUI, 1);
    sendCanMessage(message);

    // 参数3初始化
    message.paramIndex = 2;
    message.paramValue = getModuleParameterValue(moduleUI, 2);
    sendCanMessage(message);

    DBG("Sent initialization parameters to module. Type: " + juce::String(moduleType) +
       ", Index: " + juce::String(moduleIndex));
}

void HardwareModuleComponent::updateModuleParameter(uint8_t moduleType, uint8_t moduleIndex,
                                                uint8_t paramIndex, uint8_t paramValue)
{
    // 查找指定类型和索引的模块
    ModuleUI* moduleUI = findModuleByTypeAndIndex(moduleType, moduleIndex);
    if (!moduleUI)
    {
        DBG("Cannot find module to update. Type: " + juce::String(moduleType) +
           ", Index: " + juce::String(moduleIndex));
        return;
    }

    // 获取模块的实际处理器
    juce::AudioProcessor* processor = nullptr;
    processor = moduleManager->getModuleSystem()->getProcessorForNode(moduleUI->nodeId);

    if (processor == nullptr)
    {
        DBG("Cannot find processor for module");
        return;
    }

    // 根据不同的模块类型和参数索引更新参数
    // 使用APVTS访问参数
    juce::AudioProcessorValueTreeState* apvts = nullptr;
    juce::String paramId;

    // 根据不同的处理器类型获取参数树并设置参数ID
    if (auto* oscProcessor = dynamic_cast<OscillatorProcessor*>(processor))
    {
        apvts = &oscProcessor->getParameterTree();
        switch (paramIndex)
        {
            case 0: paramId = "frequency"; break;
            case 1: paramId = "waveform"; break;
            case 2: paramId = "gain"; break;
            default: return;
        }
    }
    else if (auto* delayProcessor = dynamic_cast<DelayProcessor*>(processor))
    {
        apvts = &delayProcessor->getParameterTree();
        switch (paramIndex)
        {
            case 0: paramId = "delayTime"; break;
            case 1: paramId = "feedback"; break;
            case 2: paramId = "mix"; break;
            default: return;
        }
    }
    else if (auto* envProcessor = dynamic_cast<EnvelopeGeneratorProcessor*>(processor))
    {
        apvts = &envProcessor->getParameterTree();
        switch (paramIndex)
        {
            case 0: paramId = "attack"; break;
            case 1: paramId = "hold"; break;
            case 2: paramId = "decay"; break;
            case 3: paramId = "sustain"; break;
            case 4: paramId = "release"; break;
            default: return;
        }
    }
    // 添加其他处理器类型的处理...

    // 如果成功获取APVTS和参数ID，更新参数值
    if (apvts != nullptr && !paramId.isEmpty())
    {
        if (auto* param = apvts->getParameter(paramId))
        {
            // 计算标准化参数值 (0.0-1.0)
            float normValue = paramValue / 127.0f;
            param->setValueNotifyingHost(normValue);

            DBG("Updated parameter: Module=" + moduleUI->uniqueName +
                ", Param=" + paramId +
                ", Value=" + juce::String(normValue));
        }
    }
}

juce::String HardwareModuleComponent::getModuleTypeString(uint8_t moduleType)
{
    // 模块类型索引映射到moduleInfoTable中的项
    if (moduleType < std::size(SynthModules::moduleInfoTable))
        return SynthModules::moduleInfoTable[moduleType].fullName;

    return {};
}

uint8_t HardwareModuleComponent::getModuleTypeIndex(const juce::String& moduleType)
{
    // 从moduleInfoTable中查找匹配的项
    for (size_t i = 0; i < std::size(SynthModules::moduleInfoTable); ++i)
    {
        if (moduleType == SynthModules::moduleInfoTable[i].fullName ||
            moduleType == SynthModules::moduleInfoTable[i].shortName)
            return static_cast<uint8_t>(i);
    }

    return 255; // 无效值
}

uint8_t HardwareModuleComponent::getModuleParameterValue(ModuleUI* moduleUI, uint8_t paramIndex)
{
    if (!moduleUI)
        return 0;

    // 获取模块的实际处理器
    juce::AudioProcessor* processor = nullptr;
    processor = moduleManager->getModuleSystem()->getProcessorForNode(moduleUI->nodeId);

    if (processor == nullptr)
        return 0;

    // 使用APVTS访问参数
    juce::AudioProcessorValueTreeState* apvts = nullptr;
    juce::String paramId;

    // 根据不同的处理器类型获取参数树并设置参数ID
    if (auto* oscProcessor = dynamic_cast<OscillatorProcessor*>(processor))
    {
        apvts = &oscProcessor->getParameterTree();
        switch (paramIndex)
        {
            case 0: paramId = "frequency"; break;
            case 1: paramId = "waveform"; break;
            case 2: paramId = "gain"; break;
            default: return 0;
        }
    }
    else if (auto* delayProcessor = dynamic_cast<DelayProcessor*>(processor))
    {
        apvts = &delayProcessor->getParameterTree();
        switch (paramIndex)
        {
            case 0: paramId = "delayTime"; break;
            case 1: paramId = "feedback"; break;
            case 2: paramId = "mix"; break;
            default: return 0;
        }
    }
    else if (auto* envProcessor = dynamic_cast<EnvelopeGeneratorProcessor*>(processor))
    {
        apvts = &envProcessor->getParameterTree();
        switch (paramIndex)
        {
            case 0: paramId = "attack"; break;
            case 1: paramId = "hold"; break;
            case 2: paramId = "decay"; break;
            case 3: paramId = "sustain"; break;
            case 4: paramId = "release"; break;
            default: return 0;
        }
    }
    // 添加其他处理器类型的处理...

    // 如果成功获取APVTS和参数ID，获取参数值
    if (apvts != nullptr && !paramId.isEmpty())
    {
        if (auto* param = apvts->getParameter(paramId))
        {
            // 从标准化值(0.0-1.0)转换为0-127的范围
            float normValue = param->getValue();
            return static_cast<uint8_t>(normValue * 127.0f);
        }
    }

    return 0;
}

void HardwareModuleComponent::saveProject()
{
    DBG("HardwareModuleComponent::saveProject() called");

    // 确保项目管理器已创建
    if (projectManager == nullptr)
    {
        DBG("Error: projectManager is null");
        juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                                             "Save Failed",
                                             "Project manager not initialized.");
        return;
    }

    // 创建一个对话框，让用户选择保存方式
    saveDialogPtr = std::make_shared<juce::AlertWindow>("Save Project", "Choose save method:", juce::AlertWindow::QuestionIcon);

    saveDialogPtr->addButton("Use File Browser", 1);
    saveDialogPtr->addButton("Save to Documents", 2);
    saveDialogPtr->addButton("Save to Desktop", 3);
    saveDialogPtr->addButton("Save to Current Directory", 4);
    saveDialogPtr->addButton("Cancel", 0);

    DBG("Showing save method dialog");
    saveDialogPtr->enterModalState(true, juce::ModalCallbackFunction::create(
        [this](int result) {
            if (result == 1) // 使用文件浏览器
            {
                // 创建共享指针以确保FileChooser在异步操作期间存活
                auto chooserPtr = std::make_shared<juce::FileChooser>("Save Synthesizer Project",
                                                                   currentProjectFile.existsAsFile() ? currentProjectFile : juce::File::getSpecialLocation(juce::File::userDocumentsDirectory),
                                                                   ProjectManager::getFileFilters());

                // 显示保存对话框
                auto flags = juce::FileBrowserComponent::saveMode |
                             juce::FileBrowserComponent::canSelectFiles |
                             juce::FileBrowserComponent::warnAboutOverwriting;

                DBG("Launching file chooser dialog...");

                chooserPtr->launchAsync(flags, [this, chooserPtr](const juce::FileChooser& fc)
                {
                    DBG("File chooser callback executed");
                    auto result = fc.getResult();
                    DBG("Selected file: " + (result.existsAsFile() ? result.getFullPathName() : "none"));

                    // 处理文件选择结果
                    handleAsyncSaveResult(result.existsAsFile() ? 1 : 0, result);
                });
            }
            else if (result == 2) // 保存到文档文件夹
            {
                // 生成一个默认文件名
                juce::String defaultFileName = "SynthProject_" + juce::Time::getCurrentTime().formatted("%Y%m%d_%H%M%S") + ProjectManager::getDefaultFileExtension();
                juce::File documentsDir = juce::File::getSpecialLocation(juce::File::userDocumentsDirectory);
                juce::File saveFile = documentsDir.getChildFile(defaultFileName);

                DBG("Saving directly to Documents folder: " + saveFile.getFullPathName());
                handleAsyncSaveResult(1, saveFile);
            }
            else if (result == 3) // 保存到桌面
            {
                // 生成一个默认文件名
                juce::String defaultFileName = "SynthProject_" + juce::Time::getCurrentTime().formatted("%Y%m%d_%H%M%S") + ProjectManager::getDefaultFileExtension();
                juce::File desktopDir = juce::File::getSpecialLocation(juce::File::userDesktopDirectory);
                juce::File saveFile = desktopDir.getChildFile(defaultFileName);

                DBG("Saving directly to Desktop: " + saveFile.getFullPathName());
                handleAsyncSaveResult(1, saveFile);
            }
            else if (result == 4) // 保存到当前目录
            {
                // 生成一个默认文件名
                juce::String defaultFileName = "SynthProject_" + juce::Time::getCurrentTime().formatted("%Y%m%d_%H%M%S") + ProjectManager::getDefaultFileExtension();
                juce::File currentDir = juce::File::getCurrentWorkingDirectory();
                juce::File saveFile = currentDir.getChildFile(defaultFileName);

                DBG("Saving directly to current directory: " + saveFile.getFullPathName());
                handleAsyncSaveResult(1, saveFile);
            }
        }
    ));

    DBG("saveProject() method completed");
}

void HardwareModuleComponent::loadProject()
{
    // 创建文件选择器
    juce::FileChooser chooser("Load Synthesizer Project",
                             juce::File::getSpecialLocation(juce::File::userDocumentsDirectory),
                             ProjectManager::getFileFilters());

    // 显示打开对话框
    chooser.launchAsync(juce::FileBrowserComponent::openMode, [this](const juce::FileChooser& fc)
    {
        // 处理文件选择结果
        handleAsyncLoadResult(fc.getResult().exists() ? 1 : 0, fc.getResult());
    });
}

void HardwareModuleComponent::handleAsyncSaveResult(int result, const juce::File& file)
{
    DBG("handleAsyncSaveResult called with result: " + juce::String(result));

    if (result != 0)
    {
        // 用户选择了文件
        juce::File fileToUse = file;
        DBG("Original file path: " + fileToUse.getFullPathName());

        // 确保文件有正确的扩展名
        if (!fileToUse.hasFileExtension(ProjectManager::getDefaultFileExtension()))
        {
            fileToUse = fileToUse.withFileExtension(ProjectManager::getDefaultFileExtension().substring(1));
            DBG("Adjusted file path with extension: " + fileToUse.getFullPathName());
        }

        // 检查文件路径是否有效
        juce::File parentDir = fileToUse.getParentDirectory();
        if (!parentDir.exists())
        {
            DBG("Parent directory does not exist: " + parentDir.getFullPathName());
            if (!parentDir.createDirectory())
            {
                DBG("Failed to create parent directory");
                juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                                                     "Save Failed",
                                                     "Could not create directory: " + parentDir.getFullPathName());
                return;
            }
        }

        // 检查文件是否可写
        if (fileToUse.existsAsFile() && !fileToUse.hasWriteAccess())
        {
            DBG("File exists but is not writable: " + fileToUse.getFullPathName());
            juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                                                 "Save Failed",
                                                 "File is not writable: " + fileToUse.getFullPathName());
            return;
        }

        // 保存项目
        DBG("Attempting to save project to: " + fileToUse.getFullPathName());
        bool saveResult = projectManager->saveProject(fileToUse);
        DBG("Save result: " + juce::String(saveResult ? "success" : "failed"));

        if (saveResult)
        {
            // 保存成功
            currentProjectFile = fileToUse;
            DBG("Project saved to: " + fileToUse.getFullPathName());
            DBG("File exists after save: " + juce::String(fileToUse.existsAsFile() ? "yes" : "no"));

            // 显示成功消息
            juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::InfoIcon,
                                                 "Save Successful",
                                                 "Project saved to: " + fileToUse.getFullPathName());
        }
        else
        {
            // 保存失败
            DBG("Save operation failed");
            juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                                                 "Save Failed",
                                                 "Could not save project to file: " + fileToUse.getFullPathName());
        }
    }
    else
    {
        DBG("User cancelled file selection");
    }
}

void HardwareModuleComponent::handleAsyncLoadResult(int result, const juce::File& file)
{
    if (result != 0)
    {
        // 用户选择了文件
        // 加载项目
        if (projectManager->loadProject(file))
        {
            // 加载成功
            currentProjectFile = file;
            DBG("Project loaded from: " + file.getFullPathName());

            // 更新UI
            resized();
        }
        else
        {
            // 加载失败
            juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                                                 "Load Failed",
                                                 "Could not load project from file: " + file.getFullPathName());
        }
    }
}