#pragma once

#include <JuceHeader.h>
#include <vector>

/**
 * @class AudioGraphSnapshot
 * @brief Class for saving and restoring audio processing graph state
 * 
 * This class is used when <PERSON><PERSON><PERSON> Size or sample rate changes,
 * to capture the entire audio processing graph state and restore it after rebuilding.
 */
class AudioGraphSnapshot
{
public:
    // Module Info
    struct ModuleInfo {
        juce::String name;         // Unique name
        juce::String type;         // Module type
        juce::Point<int> position;  // UI position
        juce::MemoryBlock state;    // Processor state
    };
    
    // Connection Info
    struct ConnectionInfo {
        juce::String sourceModule;
        juce::String destModule;
        int sourcePort = 0;
        int destPort = 0;
        bool isMidiConnection = false;
    };
    
    // Version Info
    int version = 1;
    
    // Audio settings
    double sampleRate = 44100.0;
    int bufferSize = 512;
    
    // Module and connection lists
    std::vector<ModuleInfo> modules;
    std::vector<ConnectionInfo> connections;
    
    // Compatibility check
    bool isCompatibleWith(int currentVersion) const 
    {
        return version <= currentVersion;  // Backward compatible
    }
    
    // Clear all data
    void clear() 
    {
        modules.clear();
        connections.clear();
    }

    // Serialize method
    void serialize(juce::MemoryBlock& destData) const
    {
        juce::MemoryOutputStream stream(destData, true);
        
        // Write audio settings
        stream.writeDouble(sampleRate);
        stream.writeInt(bufferSize);
        
        // Write module count and module information
        stream.writeInt(static_cast<int>(modules.size()));
        for (const auto& module : modules)
        {
            stream.writeString(module.name);
            stream.writeString(module.type);
            stream.writeInt(module.position.getX());
            stream.writeInt(module.position.getY());
            stream.writeInt(static_cast<int>(module.state.getSize()));
            stream.write(module.state.getData(), module.state.getSize());
        }
        
        // Write connection count and connection information
        stream.writeInt(static_cast<int>(connections.size()));
        for (const auto& conn : connections)
        {
            stream.writeString(conn.sourceModule);
            stream.writeString(conn.destModule);
            stream.writeInt(conn.sourcePort);
            stream.writeInt(conn.destPort);
            stream.writeBool(conn.isMidiConnection);
        }
    }
    
    bool deserialize(const void* data, size_t sizeInBytes)
    {
        juce::MemoryInputStream stream(data, sizeInBytes, false);
        
        // Clear existing data
        modules.clear();
        connections.clear();
        
        // Read audio settings
        sampleRate = stream.readDouble();
        bufferSize = stream.readInt();
        
        // Read module information
        int numModules = stream.readInt();
        modules.reserve(numModules);
        
        for (int i = 0; i < numModules; ++i)
        {
            ModuleInfo moduleInfo;
            moduleInfo.name = stream.readString();
            moduleInfo.type = stream.readString();
            
            int x = stream.readInt();
            int y = stream.readInt();
            moduleInfo.position = juce::Point<int>(x, y);
            
            int stateSize = stream.readInt();
            moduleInfo.state.setSize(stateSize);
            stream.read(moduleInfo.state.getData(), stateSize);
            
            modules.push_back(moduleInfo);
        }
        
        // Read connection information
        int numConnections = stream.readInt();
        connections.reserve(numConnections);
        
        for (int i = 0; i < numConnections; ++i)
        {
            ConnectionInfo connInfo;
            connInfo.sourceModule = stream.readString();
            connInfo.destModule = stream.readString();
            connInfo.sourcePort = stream.readInt();
            connInfo.destPort = stream.readInt();
            connInfo.isMidiConnection = stream.readBool();
            
            connections.push_back(connInfo);
        }
        
        return stream.getPosition() == sizeInBytes;
    }
};