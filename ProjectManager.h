#pragma once

#include <JuceHeader.h>
#include "ModuleManager.h"

/**
 * @class ProjectManager
 * @brief Manages saving and loading of synthesizer projects
 *
 * This class is responsible for saving the current module configuration,
 * connection relationships, and parameter settings to a file,
 * and restoring these settings from a file. Uses JUCE's ValueTree system for serialization.
 */
class ProjectManager
{
public:
    /**
     * @brief Constructor
     * @param moduleManager Reference to the module manager
     */
    ProjectManager(ModuleManager& moduleManager);

    /**
     * @brief Save project to file
     * @param file File to save to
     * @return Whether saving was successful
     */
    bool saveProject(const juce::File& file);

    /**
     * @brief Load project from file
     * @param file File to load from
     * @return Whether loading was successful
     */
    bool loadProject(const juce::File& file);

    /**
     * @brief Get default file extension
     * @return Default file extension
     */
    static juce::String getDefaultFileExtension() { return ".synth"; }

    /**
     * @brief Get file filters for file chooser dialog
     * @return File filter string
     */
    static juce::String getFileFilters() { return "*.synth;*.xml"; }

private:
    /**
     * @brief Create ValueTree representation of the project
     * @return ValueTree containing project data
     */
    juce::ValueTree createProjectTree();

    /**
     * @brief Save module information to ValueTree
     * @param parentTree Parent ValueTree
     */
    void saveModules(juce::ValueTree& parentTree);

    /**
     * @brief Save connection information to ValueTree
     * @param parentTree Parent ValueTree
     */
    void saveConnections(juce::ValueTree& parentTree);

    /**
     * @brief Restore modules from ValueTree
     * @param modulesTree ValueTree containing module information
     * @return Whether restoration was successful
     */
    bool restoreModules(const juce::ValueTree& modulesTree);

    /**
     * @brief Restore connections from ValueTree
     * @param connectionsTree ValueTree containing connection information
     * @return Whether restoration was successful
     */
    bool restoreConnections(const juce::ValueTree& connectionsTree);

    /**
     * @brief Clear current project
     * Remove all modules and connections
     */
    void clearProject();

    ModuleManager& moduleManager;

    // ValueTree identifiers
    struct Ids
    {
        static const juce::Identifier PROJECT;
        static const juce::Identifier VERSION;
        static const juce::Identifier MODULES;
        static const juce::Identifier MODULE;
        static const juce::Identifier CONNECTIONS;
        static const juce::Identifier CONNECTION;
        static const juce::Identifier TYPE;
        static const juce::Identifier NAME;
        static const juce::Identifier STATE;
        static const juce::Identifier SOURCE_MODULE;
        static const juce::Identifier SOURCE_PORT;
        static const juce::Identifier DEST_MODULE;
        static const juce::Identifier DEST_PORT;
        static const juce::Identifier IS_MIDI;
    };

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ProjectManager)
};
