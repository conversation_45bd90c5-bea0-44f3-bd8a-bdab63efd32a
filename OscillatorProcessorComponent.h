﻿#pragma once

#include <JuceHeader.h>
#include "OscillatorProcessor.h"

class OscillatorProcessorComponent : public juce::Component,
                                     private juce::ComboBox::Listener,
                                     private juce::Slider::Listener,
                                     private juce::Timer
{
public:
    OscillatorProcessorComponent(OscillatorProcessor& p)
        : processor(p)
    {
        // 波形选择器和标签
        addAndMakeVisible(waveformLabel);
        waveformLabel.setText("Wave", juce::dontSendNotification);
        waveformLabel.setFont(juce::Font(12.0f));
        waveformLabel.setColour(juce::Label::textColourId, juce::Colours::white);
        waveformLabel.setJustificationType(juce::Justification::right);
        
        addAndMakeVisible(waveformSelector);
        waveformSelector.addItem("Sine", 1);
        waveformSelector.addItem("Square", 2);
        waveformSelector.addItem("Saw", 3);
        waveformSelector.setSelectedId(1);
        waveformSelector.addListener(this);

        // 增益滑块和标签
        addAndMakeVisible(gainLabel);
        gainLabel.setText("Gain", juce::dontSendNotification);
        gainLabel.setFont(juce::Font(12.0f));
        gainLabel.setColour(juce::Label::textColourId, juce::Colours::white);
        gainLabel.setJustificationType(juce::Justification::right);
        
        addAndMakeVisible(gainSlider);
        gainSlider.setSliderStyle(juce::Slider::LinearBar);
        gainSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 60, 20);
        gainSlider.setRange(0.0, 1.0, 0.01);
        gainSlider.setTextValueSuffix(" Gain");
        gainSlider.addListener(this);
        
        // 音高调整分组标签
        addAndMakeVisible(pitchGroupLabel);
        pitchGroupLabel.setText("Pitch Adjustment", juce::dontSendNotification);
        pitchGroupLabel.setFont(juce::Font(12.0f, juce::Font::bold));
        pitchGroupLabel.setColour(juce::Label::textColourId, juce::Colours::white);
        
        // 失谐/微调滑块和标签
        addAndMakeVisible(detuneLabel);
        detuneLabel.setText("Detune", juce::dontSendNotification);
        detuneLabel.setFont(juce::Font(12.0f));
        detuneLabel.setColour(juce::Label::textColourId, juce::Colours::white);
        detuneLabel.setJustificationType(juce::Justification::right);
        
        addAndMakeVisible(detuneSlider);
        detuneSlider.setSliderStyle(juce::Slider::LinearBar);
        detuneSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 60, 20);
        detuneSlider.setRange(-100.0, 100.0, 1.0);
        detuneSlider.setTextValueSuffix(" cents");
        detuneSlider.addListener(this);
        
        // 半音调整滑块和标签
        addAndMakeVisible(semitoneLabel);
        semitoneLabel.setText("Semitone", juce::dontSendNotification);
        semitoneLabel.setFont(juce::Font(12.0f));
        semitoneLabel.setColour(juce::Label::textColourId, juce::Colours::white);
        semitoneLabel.setJustificationType(juce::Justification::right);
        
        addAndMakeVisible(semitoneSlider);
        semitoneSlider.setSliderStyle(juce::Slider::LinearBar);
        semitoneSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 60, 20);
        semitoneSlider.setRange(-24.0, 24.0, 1.0);
        semitoneSlider.setTextValueSuffix(" semi");
        semitoneSlider.addListener(this);
        
        // 八度调整滑块和标签
        addAndMakeVisible(octaveLabel);
        octaveLabel.setText("Octave", juce::dontSendNotification);
        octaveLabel.setFont(juce::Font(12.0f));
        octaveLabel.setColour(juce::Label::textColourId, juce::Colours::white);
        octaveLabel.setJustificationType(juce::Justification::right);
        
        addAndMakeVisible(octaveSlider);
        octaveSlider.setSliderStyle(juce::Slider::LinearBar);
        octaveSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 60, 20);
        octaveSlider.setRange(-2.0, 2.0, 1.0);
        octaveSlider.setTextValueSuffix(" oct");
        octaveSlider.addListener(this);

        // 创建参数附件
        waveformAttachment = std::make_unique<juce::AudioProcessorValueTreeState::ComboBoxAttachment>(
            p.getParameterTree(), "waveform", waveformSelector);
        gainAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            p.getParameterTree(), "gain", gainSlider);
        detuneAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            p.getParameterTree(), "detune", detuneSlider);
        semitoneAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            p.getParameterTree(), "semitone", semitoneSlider);
        octaveAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            p.getParameterTree(), "octave", octaveSlider);

        // 启动定时器用于波形显示更新
        startTimerHz(30);

        // 设置统一大小
        setSize(200, 210); // 增大了高度以适应新添加的控制器
    }

    ~OscillatorProcessorComponent() override
    {
        stopTimer();
        waveformSelector.removeListener(this);
        gainSlider.removeListener(this);
        detuneSlider.removeListener(this);
        semitoneSlider.removeListener(this);
        octaveSlider.removeListener(this);
    }

    void paint(juce::Graphics& g) override
    {
        // 绘制背景
        g.fillAll(juce::Colours::darkgrey);
        
        // 绘制边框
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(1.0f), 5.0f, 1.0f);
        
        // 绘制标题
        g.setFont(15.0f);
        auto titleBounds = getLocalBounds().removeFromTop(20);
        g.drawText("Oscillator", titleBounds.reduced(5), juce::Justification::centred);
        
        // 波形显示区域
        auto waveformDisplayArea = getLocalBounds().reduced(10, 25).removeFromTop(40);
        g.setColour(juce::Colours::black);
        g.fillRect(waveformDisplayArea);
        g.setColour(juce::Colours::white);
        g.drawRect(waveformDisplayArea, 1.0f);
        
        // 绘制波形
        drawWaveform(g, waveformDisplayArea);
    }

    void resized() override
    {
        // 布局区域
        auto area = getLocalBounds().reduced(5);
        auto titleArea = area.removeFromTop(20); // 为标题留出空间
        
        // 波形显示区域
        auto waveformDisplayArea = area.removeFromTop(40);
        area.removeFromTop(5); // 间距
        
        // 控制面板区域
        auto controlsArea = area;
        int controlHeight = 20;
        int controlGap = 3;
        int labelWidth = 55;
        
        // 波形选择器
        auto rowArea = controlsArea.removeFromTop(controlHeight);
        waveformLabel.setBounds(rowArea.removeFromLeft(labelWidth));
        waveformSelector.setBounds(rowArea);
        controlsArea.removeFromTop(controlGap);
        
        // 增益滑块
        rowArea = controlsArea.removeFromTop(controlHeight);
        gainLabel.setBounds(rowArea.removeFromLeft(labelWidth));
        gainSlider.setBounds(rowArea);
        controlsArea.removeFromTop(controlGap * 2);
        
        // 音高调整分组标题
        pitchGroupLabel.setBounds(controlsArea.removeFromTop(15));
        controlsArea.removeFromTop(controlGap);
        
        // 失谐/微调滑块
        rowArea = controlsArea.removeFromTop(controlHeight);
        detuneLabel.setBounds(rowArea.removeFromLeft(labelWidth));
        detuneSlider.setBounds(rowArea);
        controlsArea.removeFromTop(controlGap);
        
        // 半音调整滑块
        rowArea = controlsArea.removeFromTop(controlHeight);
        semitoneLabel.setBounds(rowArea.removeFromLeft(labelWidth));
        semitoneSlider.setBounds(rowArea);
        controlsArea.removeFromTop(controlGap);
        
        // 八度调整滑块
        rowArea = controlsArea.removeFromTop(controlHeight);
        octaveLabel.setBounds(rowArea.removeFromLeft(labelWidth));
        octaveSlider.setBounds(rowArea);
    }

    // 获取处理器的引用
    OscillatorProcessor* getProcessor() const { return &processor; }

    // 释放参数连接
    void releaseAttachments()
    {
        // 先释放资源
        waveformAttachment.reset(nullptr);
        gainAttachment.reset(nullptr);
        detuneAttachment.reset(nullptr);
        semitoneAttachment.reset(nullptr);
        octaveAttachment.reset(nullptr);
    }
    
    // 重建参数连接
    void rebuildAttachments()
    {
        // 确保之前的连接已经正确释放
        releaseAttachments();
        
        // 创建新的连接
        try {
            auto& tree = processor.getParameterTree();
            waveformAttachment = std::make_unique<juce::AudioProcessorValueTreeState::ComboBoxAttachment>(
                tree, "waveform", waveformSelector);
            gainAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "gain", gainSlider);
            detuneAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "detune", detuneSlider);
            semitoneAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "semitone", semitoneSlider);
            octaveAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "octave", octaveSlider);
        }
        catch (const std::exception& e) {
            // 安全处理异常，防止程序崩溃
            DBG("Error rebuilding attachments: " + juce::String(e.what()));
        }
    }

    void sliderValueChanged(juce::Slider* slider) override
    {
        if (slider == &gainSlider)
        {
            // 记录滑块变化的日志，帮助调试
            DBG("OscillatorProcessorComponent: Gain slider changed to " + juce::String(gainSlider.getValue()));
        }
        else if (slider == &detuneSlider)
        {
            DBG("OscillatorProcessorComponent: Detune slider changed to " + juce::String(detuneSlider.getValue()) + " cents");
        }
        else if (slider == &semitoneSlider)
        {
            DBG("OscillatorProcessorComponent: Semitone slider changed to " + juce::String(semitoneSlider.getValue()));
        }
        else if (slider == &octaveSlider)
        {
            DBG("OscillatorProcessorComponent: Octave slider changed to " + juce::String(octaveSlider.getValue()));
        }
    }

private:
    void timerCallback() override
    {
        repaint();
    }
    
    void comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged) override
    {
        if (comboBoxThatHasChanged == &waveformSelector)
        {
            repaint(); // 更新波形显示
        }
    }
    
    void drawWaveform(juce::Graphics& g, const juce::Rectangle<int>& bounds)
    {
        const int width = bounds.getWidth();
        const int height = bounds.getHeight();
        const int centerY = bounds.getCentreY();
        
        juce::Path path;
        path.startNewSubPath(bounds.getX(), centerY);
        
        // 根据当前选择的波形绘制
        int waveformType = waveformSelector.getSelectedId();
        
        for (int x = 0; x < width; ++x)
        {
            float phase = (float)x / width * juce::MathConstants<float>::twoPi;
            float value = 0.0f;
            
            // 生成不同波形
            switch (waveformType)
            {
                case 1: // 正弦波
                    value = std::sin(phase);
                    break;
                case 2: // 方波
                    value = phase < juce::MathConstants<float>::pi ? 1.0f : -1.0f;
                    break;
                case 3: // 锯齿波
                    value = 1.0f - (phase / juce::MathConstants<float>::pi);
                    if (value > 1.0f) value -= 2.0f;
                    break;
                default:
                    value = std::sin(phase);
            }
            
            float y = centerY - value * height * 0.4f;
            path.lineTo(bounds.getX() + x, y);
        }
        
        // 设置路径样式
        g.setColour(juce::Colours::orange);
        g.strokePath(path, juce::PathStrokeType(1.5f));
    }
    
    OscillatorProcessor& processor;
    
    // UI组件
    juce::Label waveformLabel;
    juce::ComboBox waveformSelector;
    
    juce::Label gainLabel;
    juce::Slider gainSlider;
    
    juce::Label pitchGroupLabel;
    
    juce::Label detuneLabel;
    juce::Slider detuneSlider;
    
    juce::Label semitoneLabel;
    juce::Slider semitoneSlider;
    
    juce::Label octaveLabel;
    juce::Slider octaveSlider;

    // 参数附件 - 自动处理参数绑定
    std::unique_ptr<juce::AudioProcessorValueTreeState::ComboBoxAttachment> waveformAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> gainAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> detuneAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> semitoneAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> octaveAttachment;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(OscillatorProcessorComponent)
}; 