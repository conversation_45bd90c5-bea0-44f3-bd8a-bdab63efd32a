﻿#include "OscillatorProcessor.h"

//==============================================================================
OscillatorProcessor::OscillatorProcessor()
    : AudioProcessor(BusesProperties()
          .withOutput("Audio Out", juce::AudioChannelSet::stereo()))
{
    // DBG("OscillatorProcessor: Initializing with stereo audio output");
    // DBG("OscillatorProcessor: MIDI input enabled: " + (acceptsMidi() ? juce::String("yes") : juce::String("no")));

    // Add default sound
    synth.addSound(new OscillatorSound());

    // Add multiple voices for polyphony (8 voices)
    for (int i = 0; i < 8; ++i)
    {
        synth.addVoice(new OscillatorVoice());
    }

    // Add parameter listeners
    parameters->addParameterListener("waveform", this);
    parameters->addParameterListener("gain", this);
    parameters->addParameterListener("frequency", this);
    parameters->addParameterListener("detune", this);
    parameters->addParameterListener("semitone", this);
    parameters->addParameterListener("octave", this);

    // DBG("OscillatorProcessor: Initialized with 8 voices for polyphony");
}

OscillatorProcessor::~OscillatorProcessor()
{
    // Remove parameter listeners
    parameters->removeParameterListener("waveform", this);
    parameters->removeParameterListener("gain", this);
    parameters->removeParameterListener("frequency", this);
    parameters->removeParameterListener("detune", this);
    parameters->removeParameterListener("semitone", this);
    parameters->removeParameterListener("octave", this);
}

void OscillatorProcessor::parameterChanged(const juce::String& parameterID, float newValue)
{
    if (parameterID == "waveform")
    {
        currentWaveform.store(static_cast<int>(newValue));
    }
    else if (parameterID == "gain")
    {
        currentGain.store(newValue);
    }
    else if (parameterID == "frequency")
    {
        currentFrequency.store(newValue);
    }
    else if (parameterID == "detune")
    {
        currentDetune.store(newValue);
    }
    else if (parameterID == "semitone")
    {
        currentSemitone.store(static_cast<int>(newValue));
    }
    else if (parameterID == "octave")
    {
        currentOctave.store(static_cast<int>(newValue));
    }
}

juce::AudioProcessorValueTreeState::ParameterLayout OscillatorProcessor::createParameterLayout()
{
    juce::AudioProcessorValueTreeState::ParameterLayout layout;

    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "gain",
        "Gain",
        juce::NormalisableRange<float>(0.0f, 1.0f),
        0.5f));

    layout.add(std::make_unique<juce::AudioParameterChoice>(
        "waveform",
        "Waveform",
        juce::StringArray("Sine", "Square", "Saw"),
        0));

    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "frequency",
        "Frequency",
        juce::NormalisableRange<float>(20.0f, 20000.0f, 0.1f, 0.2f),
        440.0f));
        
    // 添加新的音高调整参数
    layout.add(std::make_unique<juce::AudioParameterFloat>(
        "detune",
        "Detune",
        juce::NormalisableRange<float>(-100.0f, 100.0f, 1.0f),
        0.0f));
        
    layout.add(std::make_unique<juce::AudioParameterInt>(
        "semitone",
        "Semitone",
        -24, 24,
        0));
        
    layout.add(std::make_unique<juce::AudioParameterInt>(
        "octave",
        "Octave",
        -2, 2,
        0));

    return layout;
}

void OscillatorProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    juce::ignoreUnused(samplesPerBlock);

    synth.setCurrentPlaybackSampleRate(sampleRate);
    keyboardState.reset();

    // 初始化DSP处理器
    for (int i = 0; i < synth.getNumVoices(); ++i)
    {
        if (auto* voice = dynamic_cast<OscillatorVoice*>(synth.getVoice(i)))
        {
            voice->prepare(sampleRate);
        }
    }

    // Initialize parameter cache
    if (auto* gainParam = parameters->getRawParameterValue("gain"))
        currentGain.store(gainParam->load());
    if (auto* waveformParam = parameters->getRawParameterValue("waveform"))
        currentWaveform.store(static_cast<int>(waveformParam->load()));
    if (auto* frequencyParam = parameters->getRawParameterValue("frequency"))
        currentFrequency.store(frequencyParam->load());
    if (auto* detuneParam = parameters->getRawParameterValue("detune"))
        currentDetune.store(detuneParam->load());
    if (auto* semitoneParam = parameters->getRawParameterValue("semitone"))
        currentSemitone.store(static_cast<int>(semitoneParam->load()));
    if (auto* octaveParam = parameters->getRawParameterValue("octave"))
        currentOctave.store(static_cast<int>(octaveParam->load()));

    // 重置活动音符集合，确保在重新初始化后没有悬挂的音符
    activeNotes.clear();

    // 打印MIDI状态信息，用于调试
    // DBG("OscillatorProcessor::prepareToPlay - MIDI Input Enabled: " + juce::String(midiInputEnabled ? "Yes" : "No") +
    //     ", MIDI Input Node ID: " + juce::String(midiInputNodeId.uid));
}

void OscillatorProcessor::releaseResources()
{
    keyboardState.reset();
}

void OscillatorProcessor::handleMidiEvent(const juce::MidiMessage& message)
{
    bool noteWasOn = !activeNotes.empty();

    if (message.isNoteOn())
    {
        int noteNumber = message.getNoteNumber();
        if (midiChannel == 0 || message.getChannel() == midiChannel)
        {
            activeNotes.insert(noteNumber);
            if (!noteWasOn)
            {
                // 只有第一次按下时触发所有音符
                synth.noteOn(message.getChannel(), noteNumber, message.getFloatVelocity());
            }
        }
    }
    else if (message.isNoteOff())
    {
        int noteNumber = message.getNoteNumber();
        if (midiChannel == 0 || message.getChannel() == midiChannel)
        {
            activeNotes.erase(noteNumber);
            if (activeNotes.empty() && noteWasOn)
            {
                // 没有音符时触发noteOff
                synth.noteOff(message.getChannel(), noteNumber, 0.0f, true);
            }
        }
    }
    else if (message.isAllNotesOff() || message.isAllSoundOff())
    {
        if (midiChannel == 0 || message.getChannel() == midiChannel)
        {
            for (int note : activeNotes)
            {
                synth.noteOff(message.getChannel(), note, 0.0f, true);
            }
            activeNotes.clear();
        }
    }
}

void OscillatorProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    if (!midiInputEnabled)
    {
        buffer.clear();
        return;
    }

    // 在这个处理块的开始，安全地更新所有voice的参数
    updateVoiceParameters();

    // 处理MIDI消息
    for (const auto metadata : midiMessages)
    {
        auto message = metadata.getMessage();
        handleMidiEvent(message);
    }

    buffer.clear();
    synth.renderNextBlock(buffer, midiMessages, 0, buffer.getNumSamples());

    // 输出音量调试
    if (buffer.getNumSamples() > 0)
    {
        float rms = 0.0f;
        if (buffer.getNumChannels() > 0)
        {
            for (int i = 0; i < buffer.getNumSamples(); ++i)
            {
                float sample = buffer.getSample(0, i);
                rms += sample * sample;
            }
            rms = std::sqrt(rms / buffer.getNumSamples());
            if (rms > 0.001f)
            {
                // DBG("Oscillator output volume: " + juce::String(rms));
            }
        }
    }
}

void OscillatorProcessor::updateVoiceParameters()
{
    // 这个函数在音频线程的processBlock中被调用，因此是线程安全的
    for (int i = 0; i < synth.getNumVoices(); ++i)
    {
        if (auto* voice = dynamic_cast<OscillatorVoice*>(synth.getVoice(i)))
        {
            voice->setWaveform(currentWaveform.load());
            voice->setGain(currentGain.load());
            voice->setDetune(currentDetune.load());
            voice->setSemitone(currentSemitone.load());
            voice->setOctave(currentOctave.load());
        }
    }
}


void OscillatorProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    auto state = parameters->copyState();
    std::unique_ptr<juce::XmlElement> xml(state.createXml());

    // Add MIDI port state
    xml->setAttribute("midiInputEnabled", static_cast<int>(midiInputEnabled.load()));
    if (midiInputNodeId.uid != 0)
        xml->setAttribute("midiInputNodeId", static_cast<int>(midiInputNodeId.uid));

    copyXmlToBinary(*xml, destData);
}

void OscillatorProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    std::unique_ptr<juce::XmlElement> xmlState(getXmlFromBinary(data, sizeInBytes));

    if (xmlState != nullptr)
    {
        // Restore parameter state
        if (xmlState->hasTagName(parameters->state.getType()))
            parameters->replaceState(juce::ValueTree::fromXml(*xmlState));

        // Restore MIDI port state
        midiInputEnabled = (xmlState->getIntAttribute("midiInputEnabled", 0) != 0);
        if (xmlState->hasAttribute("midiInputNodeId"))
            midiInputNodeId = juce::AudioProcessorGraph::NodeID(xmlState->getIntAttribute("midiInputNodeId", 0));
    }
}