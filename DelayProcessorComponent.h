﻿#pragma once

#include <JuceHeader.h>
#include <memory>
#include "DelayProcessor.h"

class DelayProcessorComponent : public juce::Component,
                              private juce::Timer
{
public:
    DelayProcessorComponent(DelayProcessor& p)
        : processor(p)
    {
        // 设置滑块的通用样式
        auto setupSlider = [this](juce::Slider& slider, const juce::String& suffix) {
            slider.setSliderStyle(juce::Slider::LinearBar);
            slider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 55, 16);
            slider.setTextValueSuffix(suffix);
            addAndMakeVisible(slider);
        };
        
        // 延迟时间滑块
        setupSlider(delayTimeSlider, " s");
        
        // 反馈滑块
        setupSlider(feedbackSlider, " FB");
        
        // 混合滑块
        setupSlider(mixSlider, " Mix");

        // 创建参数附件
        delayTimeAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            processor.getParameterTree(), "delayTime", delayTimeSlider);
        feedbackAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            processor.getParameterTree(), "feedback", feedbackSlider);
        mixAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            processor.getParameterTree(), "mix", mixSlider);

        // 启动定时器用于延迟显示更新
        startTimerHz(30);
            
        // 设置统一大小
        setSize(200, 120);
    }
    
    ~DelayProcessorComponent() override
    {
        stopTimer();
    }
    
    // 释放参数连接
    void releaseAttachments()
    {
        delayTimeAttachment.reset(nullptr);
        feedbackAttachment.reset(nullptr);
        mixAttachment.reset(nullptr);
    }
    
    // 重建参数连接
    void rebuildAttachments()
    {
        // 确保之前的连接已经正确释放
        if (delayTimeAttachment != nullptr || feedbackAttachment != nullptr || mixAttachment != nullptr)
        {
            releaseAttachments();
        }
        
        // 创建新的连接
        try {
            auto& tree = processor.getParameterTree();
            delayTimeAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "delayTime", delayTimeSlider);
            feedbackAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "feedback", feedbackSlider);
            mixAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "mix", mixSlider);
        }
        catch (const std::exception& e) {
            // 安全处理异常
            DBG("Error rebuilding delay attachments: " + juce::String(e.what()));
        }
    }
    
    void paint(juce::Graphics& g) override
    {
        // 绘制背景
        g.fillAll(juce::Colours::darkgrey);

        // 绘制边框
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(1.0f), 5.0f, 1.0f);
        
        // 绘制标题
        g.setFont(15.0f);
        auto titleBounds = getLocalBounds().removeFromTop(20);
        g.drawText("Delay", titleBounds.reduced(5), juce::Justification::centred);
        
        // 延迟可视化区域
        auto delayDisplayArea = getLocalBounds().reduced(10, 25).removeFromTop(35);
        g.setColour(juce::Colours::black);
        g.fillRect(delayDisplayArea);
        g.setColour(juce::Colours::white);
        g.drawRect(delayDisplayArea, 1.0f);
        
        // 绘制延迟反馈效果图
        drawDelayVisualization(g, delayDisplayArea);
    }

    void resized() override
    {
        // 布局区域
        auto area = getLocalBounds().reduced(5);
        area.removeFromTop(20); // 为标题留出空间
        
        // 延迟可视化区域
        auto delayDisplayArea = area.removeFromTop(35);
        area.removeFromTop(3); // 间距
        
        // 控制面板区域
        auto controlsArea = area;
        int controlHeight = 20; // 控件高度
        int controlGap = 2;     // 控件间距
        
        // 延迟时间滑块
        delayTimeSlider.setBounds(controlsArea.removeFromTop(controlHeight));
        controlsArea.removeFromTop(controlGap);
        
        // 反馈滑块
        feedbackSlider.setBounds(controlsArea.removeFromTop(controlHeight));
        controlsArea.removeFromTop(controlGap);
        
        // 混合滑块
        mixSlider.setBounds(controlsArea.removeFromTop(controlHeight));
    }

private:
    void timerCallback() override
    {
        // 重绘界面以更新延迟可视化
        repaint();
    }
    
    void drawDelayVisualization(juce::Graphics& g, const juce::Rectangle<int>& bounds)
    {
        const int width = bounds.getWidth();
        const int height = bounds.getHeight();
        const int centerY = bounds.getCentreY();
        
        // 获取延迟参数值
        float delayTime = (float)delayTimeSlider.getValue();
        float feedback = (float)feedbackSlider.getValue();
        float mix = (float)mixSlider.getValue();
        
        // 添加一些变化以模拟输入信号
        float time = (float)juce::Time::getMillisecondCounterHiRes() * 0.001f;
        
        // 绘制双声道指示符 - 在左侧标记L和R
        g.setColour(juce::Colours::white);
        g.setFont(10.0f);
        g.drawText("L", 2, bounds.getY() + 2, 10, 10, juce::Justification::left, false);
        g.drawText("R", 2, bounds.getY() + bounds.getHeight()/2 + 2, 10, 10, juce::Justification::left, false);
        
        // 分隔线 - 分隔左右声道的显示区域
        g.setColour(juce::Colours::grey);
        g.drawLine(bounds.getX(), centerY, bounds.getRight(), centerY, 0.5f);
        
        // 左声道区域
        juce::Rectangle<int> leftChannelBounds(bounds.getX(), bounds.getY(), width, height/2);
        int leftCenterY = leftChannelBounds.getCentreY();
        
        // 右声道区域
        juce::Rectangle<int> rightChannelBounds(bounds.getX(), centerY, width, height/2);
        int rightCenterY = rightChannelBounds.getCentreY();
        
        // 左声道原始信号
        drawChannelSignal(g, leftChannelBounds, leftCenterY, delayTime, feedback, mix, juce::Colours::green, juce::Colours::orange);
        
        // 右声道原始信号 - 稍微偏移以区分
        drawChannelSignal(g, rightChannelBounds, rightCenterY, delayTime, feedback, mix, juce::Colours::lightgreen, juce::Colours::orange.withAlpha(0.8f));
        
        // 绘制混合效果指示器
        g.setColour(juce::Colours::white);
        g.setFont(10.0f);
        g.drawText("Dry", bounds.getX() + 12, bounds.getBottom() - 12, 20, 10, juce::Justification::left, false);
        g.drawText("Wet", bounds.getRight() - 32, bounds.getBottom() - 12, 20, 10, juce::Justification::right, false);
        
        // 绘制混合滑块位置
        int mixPosition = bounds.getX() + static_cast<int>(mix * width);
        g.setColour(juce::Colours::yellow);
        g.drawLine(mixPosition, bounds.getY(), mixPosition, bounds.getBottom(), 1.5f);
        
        // 添加"Mix"标签
        g.setColour(juce::Colours::yellow);
        g.setFont(10.0f);
        g.drawText("Mix", mixPosition - 8, bounds.getY() + 2, 16, 10, juce::Justification::centred, false);
    }
    
    // 新增辅助函数，用于绘制单个声道的信号
    void drawChannelSignal(juce::Graphics& g, const juce::Rectangle<int>& bounds, int centerY, 
                          float delayTime, float feedback, float mix,
                          juce::Colour signalColour, juce::Colour echoColour)
    {
        const int width = bounds.getWidth();
        const int height = bounds.getHeight();
        
        int pulseWidth = 8;
        int pulseStart = bounds.getX() + width / 4 - pulseWidth / 2;
        int pulseEnd = pulseStart + pulseWidth;
        
        // 绘制原始信号
        juce::Path originalPath;
        originalPath.startNewSubPath(bounds.getX(), centerY);
        originalPath.lineTo(pulseStart, centerY);
        originalPath.lineTo(pulseStart, centerY - height * 0.4f);
        originalPath.lineTo(pulseEnd, centerY - height * 0.4f);
        originalPath.lineTo(pulseEnd, centerY);
        originalPath.lineTo(bounds.getRight(), centerY);
        
        g.setColour(signalColour);
        g.strokePath(originalPath, juce::PathStrokeType(1.0f));
        
        // 绘制延迟反馈效果
        int delayOffset = static_cast<int>((delayTime / 2.0f) * width * 0.7f);
        float currentFeedback = feedback;
        
        // 绘制多次延迟反馈
        for (int i = 0; i < 4 && currentFeedback > 0.05f; ++i)
        {
            int echoStart = pulseStart + delayOffset * (i + 1);
            
            // 如果回声超出了边界，就不再绘制
            if (echoStart + pulseWidth > bounds.getRight())
                break;
                
            int echoEnd = echoStart + pulseWidth;
            float echoHeight = height * 0.4f * currentFeedback;
            
            juce::Path echoPath;
            echoPath.startNewSubPath(bounds.getX(), centerY);
            echoPath.lineTo(echoStart, centerY);
            echoPath.lineTo(echoStart, centerY - echoHeight);
            echoPath.lineTo(echoEnd, centerY - echoHeight);
            echoPath.lineTo(echoEnd, centerY);
            echoPath.lineTo(bounds.getRight(), centerY);
            
            // 使用不同的颜色显示延迟反馈
            g.setColour(echoColour.withAlpha(0.8f - i * 0.15f));
            g.strokePath(echoPath, juce::PathStrokeType(0.8f));
            
            // 减少下一次反馈的强度
            currentFeedback *= feedback;
        }
    }

    DelayProcessor& processor;

    // 滑块控件
    juce::Slider delayTimeSlider;
    juce::Slider feedbackSlider;
    juce::Slider mixSlider;

    // 参数附件 - 自动处理参数绑定
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> delayTimeAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> feedbackAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> mixAttachment;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(DelayProcessorComponent)
}; 