﻿#pragma once

#include <JuceHeader.h>
#include "MidiInputProcessor.h"

class MidiInputProcessorComponent : public juce::Component
{
public:
    MidiInputProcessorComponent(MidiInputProcessor& p)
        : processor(p)
    {
        setSize(200, 120);
    }

    void paint(juce::Graphics& g) override
    {
        // 绘制背景
        g.fillAll(juce::Colours::darkgrey);
        
        // 绘制边框
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(2), 5.0f, 2.0f);
        
        // 绘制标题
        g.setFont(15.0f);
        g.drawText("MIDI Input", getLocalBounds().removeFromTop(30),
                  juce::Justification::centred, true);
    }

    void resized() override
    {
        // 简化版本不需要额外的控件
    }

    MidiInputProcessor* getProcessor() const { return &processor; }

private:
    MidiInputProcessor& processor;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MidiInputProcessorComponent)
}; 