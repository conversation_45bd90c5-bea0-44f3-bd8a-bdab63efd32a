#include "TestMidiOutProcessor.h"

TestMidiOutProcessor::TestMidiOutProcessor()
    : AudioProcessor(BusesProperties())  // No audio buses needed
{
    DBG("TestMidiOutProcessor: Initializing");
}

TestMidiOutProcessor::~TestMidiOutProcessor()
{
}

void TestMidiOutProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    currentSampleRate = sampleRate;
    samplesUntilNextNote = 0;
    currentState = State::Silence;  // Start in silence state
    DBG("TestMidiOutProcessor: Prepared with sample rate: " + juce::String(sampleRate));
    DBG("TestMidiOutProcessor: Block size: " + juce::String(samplesPerBlock));
    DBG("TestMidiOutProcessor: MIDI output enabled: " + juce::String(producesMidi() ? "yes" : "no"));
}

void TestMidiOutProcessor::releaseResources()
{
}

void TestMidiOutProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    midiMessages.clear();
    
    auto numSamples = buffer.getNumSamples();
    int currentSample = 0;
    
    while (currentSample < numSamples)
    {
        if (samplesUntilNextNote <= 0)
        {
            switch (currentState)
            {
                case State::Silence:
                {
                    // 发送中央C的Note On
                    auto message1 = juce::MidiMessage::noteOn(midiChannel, midiNote, (juce::uint8)midiVelocity);
                    midiMessages.addEvent(message1, currentSample);
                    
                    // 发送高八度C的Note On
                    auto message2 = juce::MidiMessage::noteOn(midiChannel, midiNote + 12, (juce::uint8)midiVelocity);
                    midiMessages.addEvent(message2, currentSample);
                    
                    samplesUntilNextNote = noteDurationSamples;
                    currentState = State::Playing;
                    // DBG("TestMidiOutProcessor: [" + juce::String(juce::Time::getCurrentTime().toString(true, true, true, true)) + 
                    //     "] Note On - Channel: " + juce::String(midiChannel) + 
                    //     ", Notes: " + juce::String(midiNote) + " and " + juce::String(midiNote + 12) +
                    //     " (Middle C and High C), Duration: 1 second");
                    break;
                }
                
                case State::Playing:
                {
                    // 发送中央C的Note Off
                    auto message1 = juce::MidiMessage::noteOff(midiChannel, midiNote);
                    midiMessages.addEvent(message1, currentSample);
                    
                    // 发送高八度C的Note Off
                    auto message2 = juce::MidiMessage::noteOff(midiChannel, midiNote + 12);
                    midiMessages.addEvent(message2, currentSample);
                    
                    samplesUntilNextNote = silenceDurationSamples;
                    currentState = State::Silence;
                    // DBG("TestMidiOutProcessor: [" + juce::String(juce::Time::getCurrentTime().toString(true, true, true, true)) + 
                    //     "] Note Off - Channel: " + juce::String(midiChannel) + 
                    //     ", Notes: " + juce::String(midiNote) + " and " + juce::String(midiNote + 12) +
                    //     " (Middle C and High C), Silence: 1 second");
                    break;
                }
            }
        }
        
        int samplesThisTime = juce::jmin(numSamples - currentSample, samplesUntilNextNote);
        samplesUntilNextNote -= samplesThisTime;
        currentSample += samplesThisTime;
    }
    
    buffer.clear();
}

void TestMidiOutProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    // No state to save
}

void TestMidiOutProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    // No state to restore
} 