﻿#include "ModuleManager.h"
#include "ProcessorFactory.h"
#include "ModuleUIManager.h"
#include "ModuleConnectionManager.h"
#include <algorithm>

JUCE_BEGIN_IGNORE_WARNINGS_MSVC(4100 4244 4996)

// Define moduleInfoTable in SynthModules namespace
namespace SynthModules
{
    const ModuleInfo moduleInfoTable[] = {
        { SynthModules::ModuleType::Oscillator, "OscillatorProcessor", "osc", "Oscillators" },
        { SynthModules::ModuleType::TestOscillator, "TestOscillatorProcessor", "tosc", "Test" },
        { SynthModules::ModuleType::AudioInput, "AudioInputProcessor", "ain", "IO" },
        { SynthModules::ModuleType::AudioOutput, "AudioOutputProcessor", "aou", "IO" },
        { SynthModules::ModuleType::MidiInput, "MidiInputProcessor", "min", "IO" },
        { SynthModules::ModuleType::MidiOutput, "MidiOutputProcessor", "mou", "IO" },
        { SynthModules::ModuleType::TestMidiOutput, "TestMidiOutProcessor", "tmou", "Test" },
        { SynthModules::ModuleType::Delay, "DelayProcessor", "dey", "Effects" },
        { SynthModules::ModuleType::EnvelopeGenerator, "EnvelopeGeneratorProcessor", "env", "Modulators" },
        { SynthModules::ModuleType::Mixer, "MixerProcessor", "mix", "Processing" }
    };

    // Implement ModuleCollection class methods
    /**
     * Add an audio processor to the module collection
     *
     * @param processor Pointer to the audio processor to add
     */
    void ModuleCollection::addModule(juce::AudioProcessor* processor)
    {
        if (processor == nullptr)
            return;

        // Generate a unique ID
        juce::String id = processor->getName();

        // If ID already exists, add a number
        int count = 0;
        juce::String uniqueID = id;

        while (modules.find(uniqueID) != modules.end())
        {
            uniqueID = id + juce::String(++count);
        }

        // Add to map
        modules[uniqueID] = processor;
    }

    /**
         * Remove the specified audio processor from the module collection
         *
         * @param processor Pointer to the audio processor to remove
     */
    void ModuleCollection::removeModule(juce::AudioProcessor* processor)
    {
        if (processor == nullptr)
            return;

        // Find and remove processor
        for (auto it = modules.begin(); it != modules.end(); ++it)
        {
            if (it->second == processor)
            {
                modules.erase(it);
                return;
            }
        }
    }

    /**
     * Clear the module collection, removing all modules and counters
     */
    void ModuleCollection::clear()
    {
        modules.clear();
        counters.clear();
    }

    /**
     * Generate a unique module name based on the specified base type
     *
     * @param baseType Base type name
     * @return Generated unique name (e.g., "osc0", "osc1", etc.)
     */
    juce::String ModuleCollection::generateUniqueModuleName(const juce::String& baseType)
    {
        // Generate unique names like "osc0", "osc1", etc.
        int count = counters[baseType]++;
        return baseType + juce::String(count);
    }
}

// Implement ModuleManager class
/**
 * ModuleManager constructor
 *
 * @param deviceManager Reference to the audio device manager
 */
ModuleManager::ModuleManager(juce::AudioDeviceManager& deviceManager)
    : deviceManager(deviceManager), currentSampleRate(0), currentBlockSize(0)
{
    // Create audio graph
    moduleSystem = std::make_unique<ModuleSystemComponent>(*this);
    moduleSystem->prepareToPlay(44100.0, 512);

    // Initial configuration
    setCurrentAudioDevice(deviceManager.getCurrentAudioDevice());

    // Initialize UI manager
    uiManager = std::make_unique<ModuleUIManager>(*this);

    // Initialize connection manager
    connectionManager = std::make_unique<ModuleConnectionManager>(*this);
    
    // Initialize preset manager
    presetManager = std::make_unique<ModulePresetManager>();
    
    // Set up presets directory and file
    juce::File presetFile = juce::File::getSpecialLocation(juce::File::userApplicationDataDirectory)
                          .getChildFile("SynthApp/presets.json");
    
    // Ensure directory exists
    if (!presetFile.getParentDirectory().exists())
        presetFile.getParentDirectory().createDirectory();
    
    // Create default presets file if it doesn't exist
    if (!presetFile.existsAsFile())
    {
        createDefaultPresets(presetFile);
    }
    
    // Load presets
    presetManager->loadPresetsFromFile(presetFile);

    // Register as change listener for device manager
    deviceManager.addChangeListener(this);

    DBG("ModuleManager initialized");
}

/**
 * ModuleManager destructor
 * Releases all resources, cleans up modules and UI components
 */
ModuleManager::~ModuleManager()
{
    releaseResources();

    // Clean up UI components
    moduleUIs.clear();

    // Clean up module collection
    moduleCollection.clear();
}

/**
 * Prepare for audio processing
 *
 * @param sampleRate Sample rate
 * @param samplesPerBlock Number of samples per audio block
 */
void ModuleManager::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    currentSampleRate = sampleRate;
    currentBlockSize = samplesPerBlock;

    if (moduleSystem != nullptr)
    {
        moduleSystem->prepareToPlay(sampleRate, samplesPerBlock);
    }
}

/**
 * Process audio data block
 *
 * @param buffer Audio buffer
 * @param midiMessages MIDI message buffer
 */
void ModuleManager::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    if (moduleSystem != nullptr)
    {
        moduleSystem->processBlock(buffer, midiMessages);
    }
}

/**
 * Release module manager resources
 * Clean up UI components, module collection and audio processing graph resources
 */
void ModuleManager::releaseResources()
{
    DBG("ModuleManager::releaseResources - Releasing all resources");

    // 首先移除所有UI组件
    if (moduleUIsContainer != nullptr)
    {
        moduleUIsContainer->removeAllChildren();
        DBG("Removed all UI components from container");
    }

    // 确保所有UI组件被正确释放
    for (auto& ui : moduleUIs)
    {
        if (ui && ui->component)
        {
            // 移除所有子组件
            ui->component->removeAllChildren();

            // 确保组件不再被任何父组件持有
            if (ui->component->getParentComponent() != nullptr)
            {
                ui->component->getParentComponent()->removeChildComponent(ui->component.get());
            }

            // 明确释放组件
            ui->component = nullptr;
        }
    }

    // 清理UI组件集合
    moduleUIs.clear();
    DBG("Cleared all module UI references");

    // 已删除过时的模块名称到节点ID映射清理代码

    // 释放音频处理图资源
    if (moduleSystem != nullptr)
    {
        // 暂停处理
        if (auto* audioGraph = moduleSystem->getAudioGraph())
        {
            audioGraph->suspendProcessing(true);

            // 移除所有节点的连接，代替clearConnections
            for (auto node : audioGraph->getNodes())
            {
                audioGraph->disconnectNode(node->nodeID);
            }

            // 移除所有节点
            for (auto node : audioGraph->getNodes())
            {
                // 先调用处理器的releaseResources
                if (auto* processor = node->getProcessor())
                {
                    processor->releaseResources();
                }

                // 然后从图中移除节点
                audioGraph->removeNode(node->nodeID);
            }
        }

        // 移除所有子组件
        moduleSystem->removeAllChildren();

        // 调用系统的releaseResources方法
        moduleSystem->releaseResources();

        DBG("Released audio graph resources");
    }

    // 最后清理模块集合
    moduleCollection.clear();
    DBG("Cleared module collection");
}

/**
 * Parse module command string to get module type information
 *
 * @param command Command string (like "osc", "tosc", etc.)
 * @return Parsed module type information
 */
ModuleTypeInfo ModuleManager::parseModuleCommand(const juce::String& command)
{
    DBG("ModuleManager::parseModuleCommand - Parsing command: " + command);
    ModuleTypeInfo typeInfo;

    // Try to parse input command
    juce::String moduleType;
    if (command.contains(" "))
    {
        // If command contains space, like "osc myOsc"
        auto parts = juce::StringArray::fromTokens(command, " ", "");
        moduleType = parts[0].toLowerCase();
        typeInfo.moduleName = parts.size() > 1 ? parts[1] : "";
    }
    else
    {
        // If only module type, like "osc"
        moduleType = command.toLowerCase();
    }

    // Map short commands to full module types
    if (moduleType == "osc")
        typeInfo.fullModuleType = "oscillator";
    else if (moduleType == "tosc")
        typeInfo.fullModuleType = "testosc";
    else if (moduleType == "ain")
        typeInfo.fullModuleType = "input";
    else if (moduleType == "aou")
        typeInfo.fullModuleType = "output";
    else if (moduleType == "min")
        typeInfo.fullModuleType = "midiin";
    else if (moduleType == "mou")
        typeInfo.fullModuleType = "midiout";
    else if (moduleType == "tmou")
        typeInfo.fullModuleType = "testmidiout";
    else if (moduleType == "dey")
        typeInfo.fullModuleType = "delay";
    else if (moduleType == "env")
        typeInfo.fullModuleType = "envelopegenerator";
    else if (moduleType == "t3" || moduleType == "test3")
        typeInfo.fullModuleType = "test3"; // Mark t3 for special handling
    // else if (moduleType == "ag" || moduleType == "audiogen")
    //     typeInfo.fullModuleType = "EnvelopeGeneratorProcessor"; // AG can be used for EnvelopeGeneratorProcessor
    else
        typeInfo.fullModuleType = moduleType; // Keep original

    // Find short name
    typeInfo.shortType = moduleType;

    return typeInfo;
}

/**
 * Create an audio processor of the specified type
 *
 * @param fullModuleType Complete module type string
 * @return Unique pointer to the created audio processor
 */
std::unique_ptr<juce::AudioProcessor> ModuleManager::createProcessor(const juce::String& fullModuleType)
{
    DBG("ModuleManager::createProcessor - Creating processor for type: " + fullModuleType);
    return ProcessorFactory::create(fullModuleType, deviceManager);
}

/**
 * Generate a module name for the audio processor
 *
 * @param processor Audio processor pointer
 * @param typeInfo Module type information
 * @return Generated module name
 */
juce::String ModuleManager::generateModuleName(juce::AudioProcessor* processor, const ModuleTypeInfo& typeInfo)
{
    if (processor == nullptr)
        return {};

    // Find short name from moduleInfoTable if not provided
    juce::String shortType = typeInfo.shortType;

    if (shortType.isEmpty())
    {
        for (const auto& info : SynthModules::moduleInfoTable)
        {
            if (processor->getName() == info.className)
            {
                shortType = info.shortName;
                break;
            }
        }
    }

    if (shortType.isEmpty())
    {
        shortType = typeInfo.fullModuleType.substring(0, 3); // Default to first 3 chars
    }

    // Generate unique name
    if (typeInfo.moduleName.isEmpty())
    {
        return moduleCollection.generateUniqueModuleName(shortType);
    }

    return typeInfo.moduleName;
}

/**
 * Add an audio processor to the module system
 *
 * @param processor Audio processor pointer to add
 * @return Node ID of the added processor
 */
juce::AudioProcessorGraph::NodeID ModuleManager::addProcessorToSystem(juce::AudioProcessor* processor)
{
    juce::AudioProcessorGraph::NodeID nodeID(0);
    if (moduleSystem != nullptr && processor != nullptr)
    {
        nodeID = moduleSystem->addProcessor(processor);
    }

    return nodeID;
}

/**
 * Find node ID for a given module name
 *
 * @param moduleName Module name to look up
 * @return Found node ID, returns empty NodeID if not found
 */
juce::AudioProcessorGraph::NodeID ModuleManager::findNodeIdForName(const juce::String& moduleName) const
{
    for (const auto& moduleUI : moduleUIs)
    {
        if (moduleUI->uniqueName == moduleName)
            return moduleUI->nodeId;
    }
    return {}; // Return default constructed empty NodeID
}

// Attempting to resolve compiler parsing issue


/**
 * Create a module based on command name
 *
 * @param command Module command string
 * @return Whether creation was successful
 */
bool ModuleManager::createModuleFromName(const juce::String& command)
{
    DBG("ModuleManager::createModuleFromName - Creating processor for command: " + command);

    // Check if it's a display audio graph command (ag without parameters)
    if (command.toLowerCase() == "ag" || command.toLowerCase() == "audiograph")
    {
        // Display current audio graph state
        juce::String state = getModuleState();
        DBG(state);

        // The result should be handled by the main application to display in UI
        return true;
    }
    
    // Check if it's a preset command in our JSON presets
    if (presetManager && presetManager->hasPresetCommand(command.toLowerCase()))
    {
        DBG("Found preset command: " + command.toLowerCase());
        return createModulesFromPreset(command.toLowerCase());
    }



    // 1. Parse command and get module type info
    ModuleTypeInfo typeInfo = parseModuleCommand(command);
    if (typeInfo.fullModuleType.isEmpty())
    {
        DBG("Failed to parse command: " + command);
        return false;
    }

    // 2. Create processor
    auto processor = createProcessor(typeInfo.fullModuleType);
    if (!processor)
    {
        DBG("Failed to create module of type: " + typeInfo.fullModuleType);
        return false;
    }

    // 3. Generate module name
    juce::String moduleName = generateModuleName(processor.get(), typeInfo);

    // 4. Add to moduleSystem and get nodeID
    juce::AudioProcessorGraph::NodeID nodeID = addProcessorToSystem(processor.get());
    if (!nodeID.uid)
    {
        DBG("Failed to add module to audio graph: " + moduleName);
        return false;
    }

    // 5. Create UI component using ModuleUIManager
    auto moduleUI = uiManager->createModuleUI(processor.get(), typeInfo.fullModuleType,
                                          moduleName, nodeID);

    if (!moduleUI || !moduleUI->component)
    {
        DBG("Component creation failed for: " + moduleName);
        return false;
    }

    // 6. Add processor to collection (ownership transfer)
    moduleCollection.addModule(processor.release());

    // 7. Add UI to collection
    moduleUIs.push_back(std::move(moduleUI));

    // 8. Add component to container if we have one
    uiManager->addComponentToContainer(moduleUIs.back().get());

    DBG("Created module: " + moduleName + " of type " + typeInfo.fullModuleType);
    return true;
}


/**
 * Find a module by name
 *
 * @param name Module name
 * @return Found audio processor pointer, returns nullptr if not found
 */
juce::AudioProcessor* ModuleManager::findModuleByName(const juce::String& name)
{
    for (const auto& moduleUI : moduleUIs)
    {
        if (moduleUI && moduleUI->uniqueName == name)
        {
            if (moduleSystem != nullptr)
            {
                return moduleSystem->getProcessorForNode(moduleUI->nodeId);
            }
        }
    }

    return nullptr;
}

/**
 * Get a snapshot of the current audio graph
 *
 * @param snapshot Reference for storing snapshot data
 */
void ModuleManager::takeAudioGraphSnapshot(AudioGraphSnapshot& snapshot)
{
    DBG("Taking audio graph snapshot");

    // Save current audio settings
    snapshot.sampleRate = currentSampleRate;
    snapshot.bufferSize = currentBlockSize;

    // Capture all modules
    for (const auto& moduleUI : moduleUIs)
    {
        if (moduleUI == nullptr) continue; // Safety check

        AudioGraphSnapshot::ModuleInfo moduleInfo;
        moduleInfo.type = moduleUI->type;
        moduleInfo.name = moduleUI->uniqueName;

        // Save UI position
        if (moduleUI->component != nullptr)
        {
            auto bounds = moduleUI->component->getBounds();
            moduleInfo.position = juce::Point<int>(bounds.getX(), bounds.getY());
        }

        // Save processor state
        auto* processor = moduleSystem->getProcessorForNode(moduleUI->nodeId);
        if (processor != nullptr)
        {
            juce::MemoryBlock state;
            processor->getStateInformation(state);
            moduleInfo.state = state;
        }

        snapshot.modules.push_back(moduleInfo);
    }

    // Capture all connections
    if (connectionManager)
    {
        connectionManager->takeConnectionSnapshot(snapshot);

        DBG("Snapshot taken: " + juce::String(snapshot.modules.size()) + " modules, " +
            juce::String(snapshot.connections.size()) + " connections");
    }
}

/**
 * Restore system from audio graph snapshot
 *
 * @param snapshot Structure containing snapshot data
 */
void ModuleManager::restoreFromAudioGraphSnapshot(const AudioGraphSnapshot& snapshot)
{
    DBG("Restoring from audio graph snapshot");
    DBG("Snapshot contains " + juce::String(snapshot.modules.size()) + " modules and " +
        juce::String(snapshot.connections.size()) + " connections");

    // Save current parent component reference
    juce::Component* currentParentComponent = moduleUIsContainer;

    // Clear current module UIs
    moduleUIs.clear();

    // Remove existing components from the parent container before restoring
    if (currentParentComponent != nullptr)
    {
        currentParentComponent->removeAllChildren();
        DBG("Removed all existing child components from parent container before restoration.");
    }

    // Clear current graph
    if (moduleSystem)
    {
        moduleSystem->releaseResources();
    }
    else
    {
        // Create new module system if it doesn't exist
        moduleSystem = std::make_unique<ModuleSystemComponent>(*this);
    }

    // Update audio settings
    currentSampleRate = snapshot.sampleRate;
    currentBlockSize = snapshot.bufferSize;

    DBG("Using sample rate: " + juce::String(currentSampleRate) +
        "Hz, buffer size: " + juce::String(currentBlockSize) + " samples");

    // Create modules from snapshot
    std::map<juce::String, juce::AudioProcessorGraph::NodeID> moduleNameToNodeId;

    for (const auto& moduleInfo : snapshot.modules)
    {
        // Create processor
        auto processor = createProcessor(moduleInfo.type);

        if (processor == nullptr)
        {
            DBG("Failed to create processor of type: " + moduleInfo.type);
            continue;
        }

        // Restore processor state if available
        if (moduleInfo.state.getSize() > 0)
        {
            processor->setStateInformation(moduleInfo.state.getData(),
                                          static_cast<int>(moduleInfo.state.getSize()));
        }

        // Add processor to graph
        juce::AudioProcessorGraph::NodeID nodeId = addProcessorToSystem(processor.get());
        if (!nodeId.uid)
        {
            DBG("Failed to add processor to graph: " + moduleInfo.name);
            continue;
        }

        // Create and configure ModuleUI
        auto moduleUI = uiManager->createModuleUI(processor.get(), moduleInfo.type,
                                             moduleInfo.name, nodeId);

        if (moduleUI != nullptr)
        {
            // Set position
            moduleUI->bounds.setPosition(moduleInfo.position);

            // Map module name to node ID for connections
            moduleNameToNodeId[moduleInfo.name] = nodeId;

            // Don't add to parent component immediately - handle later

            // Add to UI collection
            moduleUIs.push_back(std::move(moduleUI));

            // Add processor to collection (ownership transfer)
            moduleCollection.addModule(processor.release());
        }
    }

    // Restore connections
    if (connectionManager)
    {
        connectionManager->restoreConnections(snapshot, moduleNameToNodeId);
    }

    // Prepare for audio processing
    moduleSystem->prepareToPlay(currentSampleRate, currentBlockSize);

    // Save moduleUIsContainer for use in AudioSettingsWindow
    moduleUIsContainer = currentParentComponent;

    // Layout UI components if parent is available
    if (currentParentComponent != nullptr)
    {
        DBG("Parent component exists, adding module system and laying out UI");

        // Re-add module system to parent - avoid adding if already added
        if (moduleSystem != nullptr)
        {
            // Check if moduleSystem already has a parent component
            juce::Component* existingParent = moduleSystem->getParentComponent();
            if (existingParent != nullptr && existingParent != currentParentComponent)
            {
                // Remove moduleSystem from its current parent first
                DBG("Removing moduleSystem from its previous parent before adding to new parent");
                existingParent->removeChildComponent(moduleSystem.get());
            }

            // Only add if it doesn't already have currentParentComponent as parent
            if (moduleSystem->getParentComponent() != currentParentComponent)
            {
                currentParentComponent->addAndMakeVisible(moduleSystem.get());
                DBG("Added moduleSystem to parent component");
            }
        }

        // For all module UIs, use the layoutModules method which already handles proper parent management
        // This avoids adding a component to a parent when it already has one, preventing the assertion failure
        uiManager->layoutModules(currentParentComponent);

        // Check visibility of all components - This check can remain, but will be more meaningful after layoutModules
        DBG("Visibility check for all module UI components:");
        for (auto& moduleUI : moduleUIs)
        {
            if (moduleUI && moduleUI->component != nullptr)
            {
                DBG(moduleUI->uniqueName + " visible: " +
                    juce::String(int(moduleUI->component->isVisible())) +
                    ", enabled: " + juce::String(int(moduleUI->component->isEnabled())));
            }
        }

        DBG("Snapshot restoration complete. Layout updated.");
    }
    else
    {
        DBG("Warning: No parent component for UI layout");
        // Parent component handling will be managed in AudioSettingsWindow destructor
    }
}

/**
 * Get a string representation of the current module system state
 *
 * @return Detailed string containing the current system state
 */
juce::String ModuleManager::getModuleState() const
{
    juce::String result;

    result += "Module System State:\n";
    result += "-----------------\n";

    // Add module information
    result += "Modules (" + juce::String(moduleUIs.size()) + "):\n";
    for (const auto& moduleUI : moduleUIs)
    {
        if (moduleUI != nullptr)
        {
            result += "  " + moduleUI->uniqueName + " (Type: " + moduleUI->type +
                    ", NodeID: " + juce::String(moduleUI->nodeId.uid) + ")\n";

            // Add more details if available
            auto* processor = moduleSystem->getProcessorForNode(moduleUI->nodeId);
            if (processor != nullptr)
            {
                result += "    - Input Channels: " + juce::String(processor->getTotalNumInputChannels()) + "\n";
                result += "    - Output Channels: " + juce::String(processor->getTotalNumOutputChannels()) + "\n";
                result += "    - Latency: " + juce::String(processor->getLatencySamples()) + " samples\n";
            }
        }
    }

    // Add connection information using connection manager
    if (connectionManager)
    {
        result += "\nConnections:\n";
        result += connectionManager->getConnectionsState();

        // Add more detailed connection visualization
        auto connections = moduleSystem->getConnections();
        if (!connections.empty())
        {
            result += "\nDetailed Connections:\n";
            for (const auto& connection : connections)
            {
                juce::String sourceNodeName = getModuleNameForNodeId(connection.source.nodeID);
                juce::String destNodeName = getModuleNameForNodeId(connection.destination.nodeID);

                result += "  " + sourceNodeName + " [" + juce::String(connection.source.channelIndex) +
                        "] -> " + destNodeName + " [" + juce::String(connection.destination.channelIndex) + "]\n";
            }
        }
    }

    // Add UI information
    result += "\nUI Components:\n";
    result += "  Total UI Components: " + juce::String(moduleUIs.size()) + "\n";

    // Add audio settings information
    result += "\nAudio Settings:\n";
    result += "  Sample Rate: " + juce::String(currentSampleRate) + " Hz\n";
    result += "  Block Size: " + juce::String(currentBlockSize) + " samples\n";

    return result;
}

/**
 * Check if a module with the specified name exists
 *
 * @param moduleName Name of the module to check
 * @return Returns true if the module exists, false otherwise
 */
bool ModuleManager::moduleExists(const juce::String& moduleName) const
{
    return std::any_of(moduleUIs.begin(), moduleUIs.end(),
                     [&moduleName](const auto& moduleUI) {
                         return moduleUI && moduleUI->uniqueName == moduleName;
                     });
}

/**
 * Set the current audio device and update related configurations
 *
 * @param device Pointer to the audio device to set
 */
void ModuleManager::setCurrentAudioDevice(juce::AudioIODevice* device)
{
    if (device == nullptr)
        return;

    // Update current audio settings
    currentSampleRate = device->getCurrentSampleRate();
    currentBlockSize = device->getCurrentBufferSizeSamples();

    DBG("Audio device updated: " + device->getName() +
        " (SR: " + juce::String(currentSampleRate) +
        "Hz, BS: " + juce::String(currentBlockSize) + " samples)");

    // Configure audio processing graph
    if (moduleSystem != nullptr)
    {
        moduleSystem->setAudioDeviceManager(deviceManager);
        moduleSystem->prepareToPlay(currentSampleRate, currentBlockSize);
    }
}

/**
 * Handle audio settings changes while preserving existing modules
 *
 * @param newDevice Pointer to the new audio device
 */
void ModuleManager::handleAudioSettingsChange(juce::AudioIODevice* newDevice)
{
    if (newDevice == nullptr)
        return;

    // Check if settings have changed
    double newSampleRate = newDevice->getCurrentSampleRate();
    int newBufferSize = newDevice->getCurrentBufferSizeSamples();

    DBG("Checking audio settings changes: Current=" + juce::String(currentSampleRate) + "Hz/" +
        juce::String(currentBlockSize) + "samples, New=" +
        juce::String(newSampleRate) + "Hz/" + juce::String(newBufferSize) + "samples");

    // If sample rate or buffer size has changed
    if (std::abs(newSampleRate - currentSampleRate) > 0.01 || newBufferSize != currentBlockSize)
    {
        DBG("Audio settings changed, updating settings without rebuilding modules");

        // Update audio settings
        currentSampleRate = newSampleRate;
        currentBlockSize = newBufferSize;

        // Configure audio processing graph without clearing modules
        if (moduleSystem != nullptr)
        {
            // Use updateAudioSettings method to update settings without deleting modules
            moduleSystem->updateAudioSettings(currentSampleRate, currentBlockSize);

            // Re-layout module UIs if needed
            if (moduleUIsContainer != nullptr)
            {
                layoutModules(moduleUIsContainer);
            }
        }

        DBG("Audio settings change completed, preserved " + juce::String(moduleUIs.size()) + " modules");
    }
    else
    {
        DBG("Audio settings unchanged, no update needed");
    }
}

/**
 * Handle callback from change broadcaster
 *
 * @param source Source broadcaster
 */
void ModuleManager::changeListenerCallback(juce::ChangeBroadcaster* source)
{
    // Check if change is from audio device manager
    if (source == &deviceManager)
    {
        DBG("Audio device settings changed");

        // Use new method to handle audio settings change
        auto* currentDevice = deviceManager.getCurrentAudioDevice();
        if (currentDevice != nullptr)
        {
            handleAudioSettingsChange(currentDevice);
        }
    }
}

/**
 * Layout module UI components in the specified container
 *
 * @param container Container component to layout modules in
 */
void ModuleManager::layoutModules(juce::Component* container)
{
    if (uiManager != nullptr && container != nullptr)
    {
        // Save parent container reference
        moduleUIsContainer = container;

        // Use a flag to prevent recursive calls
        static bool isLayouting = false;
        if (isLayouting)
            return;

        isLayouting = true;

        // First, ensure all module UI components are removed from their current parent (if any)
        for (auto& moduleUI : moduleUIs)
        {
            if (moduleUI && moduleUI->component)
            {
                // Check current component's parent
                auto* currentParent = moduleUI->component->getParentComponent();

                // If it has any parent (including our target container), remove it first
                if (currentParent != nullptr)
                {
                    DBG("Removing module UI " + moduleUI->uniqueName + " from its parent before re-adding");
                    currentParent->removeChildComponent(moduleUI->component.get());
                }
            }
        }

        // Now add all components to the container
        for (auto& moduleUI : moduleUIs)
        {
            if (moduleUI && moduleUI->component)
            {
                container->addAndMakeVisible(moduleUI->component.get());
                DBG("Added module UI to container: " + moduleUI->uniqueName);
            }
        }

        // Use UIManager for layout
        uiManager->layoutModules(container);

        // moduleSystem is handled in MainComponent::resized(), no need to add or set bounds here
        // Avoid adding moduleSystem to different parent components

        // Check visibility of all components
        DBG("Visibility check for all module UI components:");
        for (auto& moduleUI : moduleUIs)
        {
            if (moduleUI && moduleUI->component != nullptr)
            {
                DBG(moduleUI->uniqueName + " visible: " +
                    juce::String(int(moduleUI->component->isVisible())) +
                    ", enabled: " + juce::String(int(moduleUI->component->isEnabled())));
            }
        }

        isLayouting = false;
    }
}

/**
 * Get the module name for a given node ID
 *
 * @param nodeId Node ID to look up
 * @return Module name corresponding to the node ID
 */
juce::String ModuleManager::getModuleNameForNodeId(juce::AudioProcessorGraph::NodeID nodeId) const
{
    for (const auto& moduleUI : moduleUIs)
    {
        if (moduleUI->nodeId == nodeId)
            return moduleUI->uniqueName;
    }
    return "Node" + juce::String(nodeId.uid);
}

/**
 * Create a default JSON presets file with t1, t2 and t3 arrangements
 * 
 * @param presetFile File to create containing preset data
 */
void ModuleManager::createDefaultPresets(const juce::File& presetFile)
{
    // 使用字符串常量定义预设JSON
    juce::String defaultPresetsJson = 
        "{"
        "  \"commands\": ["
        "    {"
        "      \"name\": \"t1\","
        "      \"description\": \"Test arrangement with 3 oscillators (min->3*osc->mixer->aou)\","
        "      \"modules\": ["
        "        {\"shortName\": \"min\", \"type\": \"min\"},"
        "        {\"shortName\": \"osc\", \"type\": \"osc\"},"
        "        {\"shortName\": \"osc\", \"type\": \"osc\"},"
        "        {\"shortName\": \"osc\", \"type\": \"osc\"},"
        "        {\"shortName\": \"mixer\", \"type\": \"mixer\"},"
        "        {\"shortName\": \"env\", \"type\": \"env\"},"
        "        {\"shortName\": \"dey\", \"type\": \"dey\"},"       
        "        {\"shortName\": \"aou\", \"type\": \"aou\"}"
        "      ],"
        "      \"connections\": ["
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 1},"
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 2},"
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 3},"
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 5},"
        "        {\"type\": \"audio\", \"from\": 1, \"fromChannel\": 0, \"to\": 4, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 1, \"fromChannel\": 1, \"to\": 4, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 2, \"fromChannel\": 0, \"to\": 4, \"toChannel\": 2},"
        "        {\"type\": \"audio\", \"from\": 2, \"fromChannel\": 1, \"to\": 4, \"toChannel\": 3},"
        "        {\"type\": \"audio\", \"from\": 3, \"fromChannel\": 0, \"to\": 4, \"toChannel\": 4},"
        "        {\"type\": \"audio\", \"from\": 3, \"fromChannel\": 1, \"to\": 4, \"toChannel\": 5},"
        "        {\"type\": \"audio\", \"from\": 4, \"fromChannel\": 0, \"to\": 5, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 4, \"fromChannel\": 1, \"to\": 5, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 5, \"fromChannel\": 0, \"to\": 6, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 5, \"fromChannel\": 1, \"to\": 6, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 6, \"fromChannel\": 0, \"to\": 7, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 6, \"fromChannel\": 1, \"to\": 7, \"toChannel\": 1}"
        "      ]"
        "    },"
        "    {"
        "      \"name\": \"t2\","
        "      \"description\": \"Test arrangement with mixer (min->osc->env->dey->mixer->aou)\","
        "      \"modules\": ["
        "        {\"shortName\": \"min\", \"type\": \"min\"},"
        "        {\"shortName\": \"osc\", \"type\": \"osc\"},"
        "        {\"shortName\": \"env\", \"type\": \"env\"},"
        "        {\"shortName\": \"dey\", \"type\": \"dey\"},"
        "        {\"shortName\": \"mixer\", \"type\": \"mixer\"},"
        "        {\"shortName\": \"aou\", \"type\": \"aou\"}"
        "      ],"
        "      \"connections\": ["
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 1},"
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 2},"
        "        {\"type\": \"audio\", \"from\": 1, \"fromChannel\": 0, \"to\": 2, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 1, \"fromChannel\": 1, \"to\": 2, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 2, \"fromChannel\": 0, \"to\": 3, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 2, \"fromChannel\": 1, \"to\": 3, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 3, \"fromChannel\": 0, \"to\": 4, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 3, \"fromChannel\": 1, \"to\": 4, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 4, \"fromChannel\": 0, \"to\": 5, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 4, \"fromChannel\": 1, \"to\": 5, \"toChannel\": 1}"
        "      ]"
        "    },"
        "    {"
        "      \"name\": \"t3\","
        "      \"description\": \"Test arrangement without mixer (min->osc->env->dey->aou)\","
        "      \"modules\": ["
        "        {\"shortName\": \"min\", \"type\": \"min\"},"
        "        {\"shortName\": \"osc\", \"type\": \"osc\"},"
        "        {\"shortName\": \"env\", \"type\": \"env\"},"
        "        {\"shortName\": \"dey\", \"type\": \"dey\"},"
        "        {\"shortName\": \"aou\", \"type\": \"aou\"}"
        "      ],"
        "      \"connections\": ["
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 1},"
        "        {\"type\": \"midi\", \"from\": 0, \"to\": 2},"
        "        {\"type\": \"audio\", \"from\": 1, \"fromChannel\": 0, \"to\": 2, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 1, \"fromChannel\": 1, \"to\": 2, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 2, \"fromChannel\": 0, \"to\": 3, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 2, \"fromChannel\": 1, \"to\": 3, \"toChannel\": 1},"
        "        {\"type\": \"audio\", \"from\": 3, \"fromChannel\": 0, \"to\": 4, \"toChannel\": 0},"
        "        {\"type\": \"audio\", \"from\": 3, \"fromChannel\": 1, \"to\": 4, \"toChannel\": 1}"
        "      ]"
        "    }"
        "  ]"
        "}";
    
    // 将字符串写入文件
    presetFile.replaceWithText(defaultPresetsJson);
    DBG("Created default presets file at " + presetFile.getFullPathName());
}

/**
 * Create modules from a named preset
 *
 * @param presetName Name of the preset to load
 * @return True if modules were successfully created
 */
bool ModuleManager::createModulesFromPreset(const juce::String& presetName)
{
    if (!presetManager || !presetManager->hasPresetCommand(presetName))
    {
        DBG("Preset not found: " + presetName);
        return false;
    }
    
    juce::var config = presetManager->getCommandConfig(presetName);
    
    // Extract module types
    juce::Array<juce::String> moduleTypes;
    if (auto* modules = config["modules"].getArray())
    {
        for (const auto& module : *modules)
        {
            moduleTypes.add(module["type"].toString());
        }
    }
    
    // Extract connection configurations
    juce::Array<juce::var> connections;
    if (auto* conns = config["connections"].getArray())
    {
        for (const auto& conn : *conns)
        {
            connections.add(conn);
        }
    }
    
    DBG("Creating module arrangement from preset: " + presetName);
    return createModuleArrangement(moduleTypes, connections);
}

/**
 * Create a module arrangement with the specified modules and connections
 *
 * @param moduleTypes Array of module type identifiers
 * @param connections Array of connection configurations
 * @return True if the arrangement was successfully created
 */
bool ModuleManager::createModuleArrangement(const juce::Array<juce::String>& moduleTypes, 
                                           const juce::Array<juce::var>& connections)
{
    DBG("Creating module arrangement with " + juce::String(moduleTypes.size()) + " modules");
    
    // Module info storage
    struct ModuleCreationInfo {
        juce::String name;
        juce::String type;
        juce::AudioProcessorGraph::NodeID nodeId;
    };
    
    std::vector<ModuleCreationInfo> modules;
    
    // Create all modules
    for (const auto& type : moduleTypes)
    {
        if (!createModuleFromName(type))
        {
            DBG("Failed to create module of type: " + type);
            return false;
        }
        modules.push_back({moduleUIs.back()->uniqueName, type, moduleUIs.back()->nodeId});
        DBG("Created module: " + modules.back().name + " of type " + type);
    }
    
    // Create connections
    if (connectionManager != nullptr)
    {
        for (const auto& connVar : connections)
        {
            juce::String connType = connVar["type"].toString();
            int fromIdx = static_cast<int>(connVar["from"]);
            int toIdx = static_cast<int>(connVar["to"]);
            
            // Check for valid indices
            if (fromIdx < 0 || fromIdx >= static_cast<int>(modules.size()) || 
                toIdx < 0 || toIdx >= static_cast<int>(modules.size()))
            {
                DBG("Invalid connection indices: " + juce::String(fromIdx) + " -> " + juce::String(toIdx));
                continue;
            }
            
            // Process by connection type
            if (connType == "midi")
            {
                if (!connectionManager->connectMidiProcessors(modules[fromIdx].nodeId, modules[toIdx].nodeId))
                {
                    DBG("Failed to connect MIDI: " + modules[fromIdx].name + " -> " + modules[toIdx].name);
                }
                else
                {
                    DBG("Connected MIDI: " + modules[fromIdx].name + " -> " + modules[toIdx].name);
                }
            }
            else if (connType == "audio")
            {
                int fromChannel = static_cast<int>(connVar["fromChannel"]);
                int toChannel = static_cast<int>(connVar["toChannel"]);
                
                if (!connectionManager->connectProcessors(modules[fromIdx].nodeId, fromChannel, 
                                                        modules[toIdx].nodeId, toChannel))
                {
                    DBG("Failed to connect audio: " + modules[fromIdx].name + " ch" + juce::String(fromChannel) + 
                        " -> " + modules[toIdx].name + " ch" + juce::String(toChannel));
                }
                else
                {
                    DBG("Connected audio: " + modules[fromIdx].name + " ch" + juce::String(fromChannel) + 
                        " -> " + modules[toIdx].name + " ch" + juce::String(toChannel));
                }
            }
        }
    }
    
    return true;
}

/**
 * Save the current module setup as a preset
 *
 * @param presetName Name to give the preset
 * @param description Optional description of the preset
 * @return True if successfully saved
 */
bool ModuleManager::saveCurrentSetupAsPreset(const juce::String& presetName, const juce::String& description)
{
    if (!presetManager)
        return false;
        
    // Collect module types in order
    juce::Array<juce::String> moduleTypes;
    for (const auto& ui : moduleUIs)
    {
        moduleTypes.add(ui->type);
    }
    
    // Collect connections
    juce::Array<juce::var> connections;
    
    if (connectionManager && moduleSystem)
    {
        // Get connections from the module system (use copies instead of references)
        std::vector<juce::AudioProcessorGraph::Connection> audioConnections = moduleSystem->getAudioConnections();
        std::vector<juce::AudioProcessorGraph::Connection> midiConnections = moduleSystem->getMidiConnections();
        
        // Helper map to find the index of a module by its NodeID
        std::map<juce::AudioProcessorGraph::NodeID, int> nodeIdToIndex;
        for (int i = 0; i < moduleUIs.size(); ++i)
        {
            nodeIdToIndex[moduleUIs[i]->nodeId] = i;
        }
        
        // Add audio connections
        for (const auto& conn : audioConnections)
        {
            if (nodeIdToIndex.find(conn.source.nodeID) != nodeIdToIndex.end() &&
                nodeIdToIndex.find(conn.destination.nodeID) != nodeIdToIndex.end())
            {
                juce::var connection(new juce::DynamicObject());
                connection.getDynamicObject()->setProperty("type", juce::var("audio"));
                connection.getDynamicObject()->setProperty("from", juce::var(nodeIdToIndex[conn.source.nodeID]));
                connection.getDynamicObject()->setProperty("fromChannel", juce::var(conn.source.channelIndex));
                connection.getDynamicObject()->setProperty("to", juce::var(nodeIdToIndex[conn.destination.nodeID]));
                connection.getDynamicObject()->setProperty("toChannel", juce::var(conn.destination.channelIndex));
                connections.add(connection);
            }
        }
        
        // Add MIDI connections
        for (const auto& conn : midiConnections)
        {
            if (nodeIdToIndex.find(conn.source.nodeID) != nodeIdToIndex.end() &&
                nodeIdToIndex.find(conn.destination.nodeID) != nodeIdToIndex.end())
            {
                juce::var connection(new juce::DynamicObject());
                connection.getDynamicObject()->setProperty("type", juce::var("midi"));
                connection.getDynamicObject()->setProperty("from", juce::var(nodeIdToIndex[conn.source.nodeID]));
                connection.getDynamicObject()->setProperty("to", juce::var(nodeIdToIndex[conn.destination.nodeID]));
                connections.add(connection);
            }
        }
    }
    
    // Save the preset
    return presetManager->savePresetToFile(presetName, moduleTypes, connections, description);
}

JUCE_END_IGNORE_WARNINGS_MSVC