#pragma once

#include <JuceHeader.h>

// Attack-Decay-Hold-Sustain-Release Envelope Generator
class ADHSREnvelope
{
public:
    ADHSREnvelope() 
    {
        reset();
    }
    
    enum class EnvelopeState
    {
        Idle,
        Attack,
        Hold,
        Decay,
        Sustain,
        Release
    };
    
    void prepare(double sampleRate)
    {
        adsr.setSampleRate(sampleRate);
        currentSampleRate = sampleRate;
        reset();
    }
    
    void reset()
    {
        adsr.reset();
        state = EnvelopeState::Idle;
        holdCounter = 0;
        
        // Initialize cached current sample value
        cachedCurrentLevel = 0.0f;
    }
    
    void setParameters(float attackTimeSeconds, float holdTimeSeconds, 
                      float decayTimeSeconds, float sustainLevel, 
                      float releaseTimeSeconds)
    {
        // Set ADSR parameters
        juce::ADSR::Parameters adsrParams;
        adsrParams.attack = attackTimeSeconds;
        adsrParams.decay = decayTimeSeconds;
        adsrParams.sustain = sustainLevel;
        adsrParams.release = releaseTimeSeconds;
        adsr.setParameters(adsrParams);
        
        // Save hold time (in samples)
        if (currentSampleRate > 0)
            holdSamples = static_cast<int>(holdTimeSeconds * currentSampleRate);
        else
            holdSamples = 0;
    }
    
    void noteOn()
    {
        state = EnvelopeState::Attack;
        holdCounter = 0;
        adsr.noteOn();
    }
    
    void noteOff()
    {
        state = EnvelopeState::Release;
        adsr.noteOff();
    }
    
    float getNextSample()
    {
        float sample = 0.0f;
        // Define sustainValue before the switch statement
        float sustainValue = adsr.getParameters().sustain;
        
        switch (state)
        {
            case EnvelopeState::Idle:
                sample = 0.0f;
                break;
                
            case EnvelopeState::Attack:
                sample = adsr.getNextSample();
                // Detect end of Attack phase - when value is close to 1.0
                if (sample > 0.99f)
                {
                    state = EnvelopeState::Hold;
                    holdCounter = 0;
                }
                break;
                
            case EnvelopeState::Hold:
                sample = 1.0f; // Hold phase maintains peak value
                
                if (++holdCounter >= holdSamples)
                {
                    state = EnvelopeState::Decay;
                    // Reset ADSR state for decay to work
                    adsr.noteOff();
                    adsr.noteOn();
                    adsr.getNextSample(); // Skip attack sample
                }
                break;
                
            case EnvelopeState::Decay:
                sample = adsr.getNextSample();
                
                // Detect end of Decay phase - when value is close to sustain level
                if (std::abs(sample - sustainValue) < 0.01f)
                {
                    state = EnvelopeState::Sustain;
                }
                break;
                
            case EnvelopeState::Sustain:
                sample = adsr.getNextSample();
                break;
                
            case EnvelopeState::Release:
                sample = adsr.getNextSample();
                
                if (adsr.isActive() == false)
                {
                    state = EnvelopeState::Idle;
                }
                break;
        }
        
        // Cache current sample value for use in const methods
        cachedCurrentLevel = sample;
        return sample;
    }
    
    // Return current envelope level (for visualization)
    float getCurrentLevel() const
    {
        // Return cached current sample value instead of calling non-const getNextSample
        return cachedCurrentLevel;
    }
    
    EnvelopeState getState() const
    {
        return state;
    }
    
private:
    juce::ADSR adsr;
    EnvelopeState state;
    int holdSamples = 0;
    int holdCounter = 0;
    float cachedCurrentLevel = 0.0f; // Cache current sample value
    double currentSampleRate = 44100.0; // Store current sample rate
};