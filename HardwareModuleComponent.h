#pragma once

#include <JuceHeader.h>
#include "ModuleManager.h"  // 包含ModuleUI定义

JUCE_BEGIN_IGNORE_WARNINGS_MSVC(4100 4244 4996)

// CAN消息结构体
struct CanMessage {
    uint8_t moduleType;    // 模块类型
    uint8_t moduleIndex;   // 模块索引
    uint8_t paramIndex;    // 参数索引
    uint8_t paramValue;    // 参数值
    
    // 其他可能需要的字段
    uint8_t additionalData[4] = {0};
};

class HardwareModuleComponent : public juce::Component,
                               private juce::Timer
{
public:
    HardwareModuleComponent();
    ~HardwareModuleComponent() override;

    // Component接口
    void paint(juce::Graphics& g) override;
    void resized() override;

    // 初始化CAN接口
    bool initializeCanInterface(const juce::String& canInterface = "can0");
    
    // 更新函数
    void timerCallback() override;
    
    // 处理从CAN总线收到的消息
    void handleCanMessage(const CanMessage& message);
    
    // 获取ModuleManager
    ModuleManager* getModuleManager() { return moduleManager.get(); }
    
    // 获取AudioProcessorGraph的辅助方法
    juce::AudioProcessorGraph& getProcessorGraph();
    
    // 根据模块类型和索引查找模块
    ModuleUI* findModuleByTypeAndIndex(uint8_t moduleType, uint8_t moduleIndex);
    
    // 发送CAN消息给硬件模块
    bool sendCanMessage(const CanMessage& message);
    
    // 创建模块
    void createModuleFromCanMessage(uint8_t moduleType);
    
    // 发送模块初始化参数
    void sendModuleInitParams(uint8_t moduleType, uint8_t moduleIndex);
    
    // 更新模块参数
    void updateModuleParameter(uint8_t moduleType, uint8_t moduleIndex, 
                              uint8_t paramIndex, uint8_t paramValue);
    
    // 将ModuleType枚举值转换为字符串
    juce::String getModuleTypeString(uint8_t moduleType);
    
    // 获取模块的参数值
    uint8_t getModuleParameterValue(ModuleUI* moduleUI, uint8_t paramIndex);

private:
    juce::AudioDeviceManager deviceManager;
    std::unique_ptr<ModuleManager> moduleManager;
    
    // CAN相关成员
    int canSocket = -1;
    bool canInterfaceActive = false;
    
    // 存储模块类型和实例索引的映射
    std::map<juce::String, std::vector<juce::AudioProcessorGraph::NodeID>> moduleTypeToInstances;
    
    // 工具函数：将ModuleType字符串转换为索引
    uint8_t getModuleTypeIndex(const juce::String& moduleType);
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(HardwareModuleComponent)
};

JUCE_END_IGNORE_WARNINGS_MSVC 