﻿#pragma once

#include <JuceHeader.h>

class MidiInputProcessor : public juce::AudioProcessorGraph::AudioGraphIOProcessor,
                          public juce::MidiInputCallback
{
public:
    explicit MidiInputProcessor(juce::AudioDeviceManager& deviceManager)
        : AudioGraphIOProcessor(juce::AudioProcessorGraph::AudioGraphIOProcessor::midiInputNode),
          deviceManager(deviceManager)
    {
        // Initialize and enable all MIDI input devices
        refreshMidiInputs();
    }
    
    ~MidiInputProcessor() override
    {
        // Close all MIDI inputs
        disableMidiInputs();
    }

    // Basic processor functions
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages) override;
    
    // MIDI input callback
    void handleIncomingMidiMessage(juce::MidiInput* source, const juce::MidiMessage& message) override
    {
        // Add received MIDI messages to processing queue
        const juce::ScopedLock sl(midiMessagesLock);
        incomingMessages.addEvent(message, 0);
        DBG("MidiInputProcessor: Received external MIDI message: " + message.getDescription());
    }
    
    // Refresh MIDI input device list
    void refreshMidiInputs()
    {
        // Disable all current MIDI inputs
        disableMidiInputs();
        
        // Get available MIDI input devices
        auto midiInputs = juce::MidiInput::getAvailableDevices();
        
        DBG("Available MIDI input devices:");
        for (const auto& input : midiInputs)
        {
            DBG(" - " + input.name);
            // Enable each MIDI device
            deviceManager.setMidiInputDeviceEnabled(input.identifier, true);
            // Add this processor as MIDI callback - 使用新方法
            deviceManager.addMidiInputDeviceCallback(input.identifier, this);
        }
        
        if (midiInputs.isEmpty())
        {
            DBG("No MIDI input devices found!");
        }
    }
    
    // Disable all MIDI inputs
    void disableMidiInputs()
    {
        auto midiInputs = juce::MidiInput::getAvailableDevices();
        for (const auto& input : midiInputs)
        {
            // 使用新方法
            deviceManager.removeMidiInputDeviceCallback(input.identifier, this);
            deviceManager.setMidiInputDeviceEnabled(input.identifier, false);
        }
    }
    
    // State management
    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

    // UI related
    juce::AudioProcessorEditor* createEditor() override { return nullptr; }
    bool hasEditor() const override { return false; }

    // Basic information
    const juce::String getName() const override { return "MIDI Input"; }
    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return true; }

    // MIDI channel control
    int getMidiChannel() const { return midiChannel; }
    void setMidiChannel(int newChannel) { midiChannel = juce::jlimit(1, 16, newChannel); }

private:
    int midiChannel = 1;
    juce::AudioDeviceManager& deviceManager;
    
    // MIDI message queue
    juce::CriticalSection midiMessagesLock;
    juce::MidiBuffer incomingMessages;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MidiInputProcessor)
}; 