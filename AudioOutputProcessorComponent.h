#pragma once

#include <JuceHeader.h>
#include "AudioOutputProcessor.h" // Include the corresponding processor header

//==============================================================================
/*
    This component displays a master volume control for the AudioOutputProcessor.
    Its style is intended to match other modules like the Envelope Generator.
*/
class AudioOutputProcessorComponent : public juce::Component
{
public:
    // Constructor: Takes a reference to the processor it represents
    explicit AudioOutputProcessorComponent(AudioOutputProcessor& p)
        : processor(p)
    {
        // Add the volume slider to the component and make it visible
        addAndMakeVisible(volumeSlider);

        // Configure the slider's appearance - Match ENV style
        volumeSlider.setSliderStyle(juce::Slider::LinearBar); // Use LinearBar like ENV
        volumeSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 40, 15); // Match ENV textbox size
        // Remove explicit text box colors to use LookAndFeel like ENV
        // volumeSlider.setColour(juce::Slider::textBoxTextColourId, juce::Colours::white);
        // volumeSlider.setColour(juce::Slider::textBoxBackgroundColourId, juce::Colour(0xff303030));
        // volumeSlider.setColour(juce::Slider::textBoxOutlineColourId, juce::Colour(0xff606060));
        volumeSlider.setTextValueSuffix(" Vol"); // Keep suffix for clarity

        // Create the attachment to link the slider to the processor's "masterVolume" parameter
        // Ensure "masterVolume" parameter exists in AudioOutputProcessor's ParameterLayout!
        volumeAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            processor.getParameterTree(), "masterVolume", volumeSlider);

        // Set the component's size (same as ENV module)
        setSize(200, 120); // Ensure size matches ENV
    }

    // Destructor: The unique_ptr for the attachment handles cleanup automatically
    ~AudioOutputProcessorComponent() override = default;

    // Paint method: Draws the component's background, border, and title - Match ENV style
    void paint(juce::Graphics& g) override
    {
        // Fill background with dark grey (like ENV)
        g.fillAll(juce::Colours::darkgrey);

        // Draw rounded border with white color (like ENV)
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(1.0f), 5.0f, 1.0f); // 5.0f radius

        // Draw the title text centered at the top (match ENV font size)
        g.setColour(juce::Colours::white);
        g.setFont(15.0f); // Match ENV font size
        auto titleArea = getLocalBounds().removeFromTop(20); // Reserve space for title
        g.drawText("Audio Output", titleArea, juce::Justification::centred, true);
    }

    // Resized method: Positions the child components (the volume slider)
    void resized() override
    {
        auto bounds = getLocalBounds();
        bounds.removeFromTop(20); // Remove title area
        bounds = bounds.reduced(10); // Add some padding around the slider

        // Place the volume slider in the remaining area
        volumeSlider.setBounds(bounds);
    }

private:
    // Reference to the audio processor
    AudioOutputProcessor& processor;

    // UI Elements
    juce::Slider volumeSlider;

    // Parameter Attachment
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> volumeAttachment;

    // Macro to prevent copying and detect memory leaks
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioOutputProcessorComponent)
};
