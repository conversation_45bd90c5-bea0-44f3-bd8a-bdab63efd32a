﻿#include "MidiInputProcessor.h"

void MidiInputProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    // Initialize processor
    juce::ignoreUnused(sampleRate, samplesPerBlock);
    
    // Clear MIDI message queue
    const juce::ScopedLock sl(midiMessagesLock);
    incomingMessages.clear();
    
    // Refresh MIDI input connections
    refreshMidiInputs();
}

void MidiInputProcessor::releaseResources()
{
    // Release resources
    disableMidiInputs();
    
    // Clear MIDI message queue
    const juce::ScopedLock sl(midiMessagesLock);
    incomingMessages.clear();
}

void MidiInputProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    // Clear output buffer
    buffer.clear();
    
    // Add messages collected from external MIDI devices
    {
        const juce::ScopedLock sl(midiMessagesLock);
        if (!incomingMessages.isEmpty())
        {
            DBG("\nMIDI Input Processor received MIDI signals from external devices:");
            for (const auto metadata : incomingMessages)
            {
                auto message = metadata.getMessage();
                DBG(" - External MIDI message: " + message.getDescription() + 
                    " Position: " + juce::String(metadata.samplePosition) + 
                    " Channel: " + juce::String(message.getChannel()) + 
                    (message.isNoteOn() ? " Note: " + juce::String(message.getNoteNumber()) + 
                                       " Velocity: " + juce::String(message.getVelocity()) : ""));
            }
            
            // Add externally collected messages to output
            midiMessages.addEvents(incomingMessages, 0, buffer.getNumSamples(), 0);
            incomingMessages.clear();
        }
    }
    
    // Process directly passed MIDI messages (e.g. from test commands)
    if (!midiMessages.isEmpty())
    {
        // Print received MIDI messages
        DBG("\nMIDI Input Processor received direct MIDI signals:");
        for (const auto metadata : midiMessages)
        {
            auto message = metadata.getMessage();
            DBG(" - Direct MIDI message: " + message.getDescription() + 
                " Position: " + juce::String(metadata.samplePosition) + 
                " Channel: " + juce::String(message.getChannel()) + 
                (message.isNoteOn() ? " Note: " + juce::String(message.getNoteNumber()) + 
                                   " Velocity: " + juce::String(message.getVelocity()) : ""));
        }
        
        // Update MIDI channel
        juce::MidiBuffer processedMessages;
        for (const auto metadata : midiMessages)
        {
            auto message = metadata.getMessage();
            if (message.getChannel() != midiChannel)
            {
                message.setChannel(midiChannel);
                DBG(" - MIDI channel adjusted to: " + juce::String(midiChannel));
            }
            processedMessages.addEvent(message, metadata.samplePosition);
        }
        
        // Reassign processed MIDI messages
        midiMessages.clear();
        midiMessages.swapWith(processedMessages);
        
        DBG("MIDI messages processed, ready to pass to downstream modules");
    }
}

void MidiInputProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    // Save state
    juce::ValueTree state("MidiInputProcessorState");
    state.setProperty("midiChannel", midiChannel, nullptr);
    
    std::unique_ptr<juce::XmlElement> xml(state.createXml());
    copyXmlToBinary(*xml, destData);
}

void MidiInputProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    // Restore state
    std::unique_ptr<juce::XmlElement> xmlState(getXmlFromBinary(data, sizeInBytes));
    
    if (xmlState != nullptr)
    {
        juce::ValueTree state = juce::ValueTree::fromXml(*xmlState);
        midiChannel = state.getProperty("midiChannel", 1);
    }
} 