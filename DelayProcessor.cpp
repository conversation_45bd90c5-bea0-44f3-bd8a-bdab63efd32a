#include "DelayProcessor.h"
#include "DelayProcessorComponent.h"

juce::AudioProcessorValueTreeState::ParameterLayout DelayProcessor::createParameterLayout()
{
    std::vector<std::unique_ptr<juce::RangedAudioParameter>> params;

    params.push_back(std::make_unique<juce::AudioParameterFloat>(
        juce::ParameterID("delayTime", 1),
        "Delay Time",
        juce::NormalisableRange<float>(0.02f, 2.0f, 0.001f, 0.3f),
        0.5f,
        "s"
    ));

    params.push_back(std::make_unique<juce::AudioParameterFloat>(
        juce::ParameterID("feedback", 1),
        "Feedback",
        juce::NormalisableRange<float>(0.0f, 0.95f, 0.01f),
        0.5f,
        "%",
        juce::AudioProcessorParameter::genericParameter,
        [](float value, int) { return juce::String(value * 100.0f, 1) + "%"; }
    ));

    params.push_back(std::make_unique<juce::AudioParameterFloat>(
        juce::ParameterID("mix", 1),
        "Mix",
        juce::NormalisableRange<float>(0.0f, 1.0f, 0.01f),
        0.5f,
        "%",
        juce::AudioProcessorParameter::genericParameter,
        [](float value, int) { return juce::String(value * 100.0f, 1) + "%"; }
    ));

    return { params.begin(), params.end() };
}

DelayProcessor::DelayProcessor()
    : AudioProcessor(BusesProperties()
          .withInput("Input", juce::AudioChannelSet::stereo())
          .withOutput("Output", juce::AudioChannelSet::stereo())),
      parameters(std::make_unique<juce::AudioProcessorValueTreeState>(
          *this, nullptr, "Parameters", createParameterLayout())),
      delayLine(192000 * 2),  // Maximum 2 second delay at 192kHz
      sampleRate(44100.0),
      delayTime(0.5f),
      feedback(0.5f),
      mix(0.5f)
{
    DBG("DelayProcessor: Initializing with stereo input/output");

    parameters->addParameterListener("delayTime", this);
    parameters->addParameterListener("feedback", this);
    parameters->addParameterListener("mix", this);

    // Initialize parameter values
    this->delayTime = parameters->getRawParameterValue("delayTime")->load();
    this->feedback = parameters->getRawParameterValue("feedback")->load();
    this->mix = parameters->getRawParameterValue("mix")->load();
    
    // Initialize smoothed values
    delayTimeSmoothed.setCurrentAndTargetValue(this->delayTime.load());
    feedbackSmoothed.setCurrentAndTargetValue(this->feedback.load());
    mixSmoothed.setCurrentAndTargetValue(this->mix.load());
}

DelayProcessor::~DelayProcessor() noexcept
{
    parameters->removeParameterListener("delayTime", this);
    parameters->removeParameterListener("feedback", this);
    parameters->removeParameterListener("mix", this);
}

void DelayProcessor::parameterChanged(const juce::String& parameterID, float newValue)
{
    if (parameterID == "delayTime")
    {
        this->delayTime = newValue;
        // Set target value for smooth transition
        delayTimeSmoothed.setTargetValue(newValue);
    }
    else if (parameterID == "feedback")
    {
        this->feedback = newValue;
        // Set target value for smooth transition
        feedbackSmoothed.setTargetValue(newValue);
    }
    else if (parameterID == "mix")
    {
        this->mix = newValue;
        // Set target value for smooth transition
        mixSmoothed.setTargetValue(newValue);
    }

    DBG("DelayProcessor: Parameter " + parameterID + " changed to " + juce::String(newValue));
}

void DelayProcessor::prepareToPlay(double newSampleRate, int samplesPerBlock)
{
    sampleRate = newSampleRate;
    
    const int numChannels = getTotalNumOutputChannels();
    
    DBG("DelayProcessor: Prepared with sample rate: " + juce::String(sampleRate) + 
        "Hz, block size: " + juce::String(samplesPerBlock) + 
        ", channels: " + juce::String(numChannels));

    juce::dsp::ProcessSpec spec;
    spec.sampleRate = newSampleRate;
    spec.maximumBlockSize = static_cast<juce::uint32>(samplesPerBlock);
    spec.numChannels = static_cast<juce::uint32>(numChannels);
    
    delayLine.prepare(spec);
    delayLine.reset();
    
    // Set initial delay time
    float initialDelayTime = delayTime.load();
    delayLine.setDelay(static_cast<float>(sampleRate) * initialDelayTime);
    
    // Use class member variable for smoothing time
    // Calculate smoothing time in seconds
    const float smoothingTimeSec = this->smoothingTimeMs * 0.001f;
    
    // Reset smoothed value processors
    delayTimeSmoothed.reset(newSampleRate, smoothingTimeSec);
    feedbackSmoothed.reset(newSampleRate, smoothingTimeSec);
    mixSmoothed.reset(newSampleRate, smoothingTimeSec);
    
    // Set initial values
    delayTimeSmoothed.setCurrentAndTargetValue(initialDelayTime);
    feedbackSmoothed.setCurrentAndTargetValue(feedback.load());
    mixSmoothed.setCurrentAndTargetValue(mix.load());
}

void DelayProcessor::releaseResources()
{
    DBG("DelayProcessor: Releasing resources");
    delayLine.reset();
}

void DelayProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer&)
{
    const int numChannels = buffer.getNumChannels();
    const int numSamples = buffer.getNumSamples();
    
    // Process each sample
    for (int channel = 0; channel < numChannels; ++channel)
    {
        auto* channelData = buffer.getWritePointer(channel);

        for (int sample = 0; sample < numSamples; ++sample)
        {
            // Get current smoothed parameter values
            float currentDelayTime = delayTimeSmoothed.getNextValue();
            float currentFeedback = feedbackSmoothed.getNextValue();
            float currentMix = mixSmoothed.getNextValue();
            
            // Update delay time if changed
            // Note: Frequent changes may cause audio artifacts
            delayLine.setDelay(static_cast<float>(sampleRate) * currentDelayTime);
            
            const float dry = channelData[sample];
            
            float wet = this->delayLine.popSample(channel);
            
            this->delayLine.pushSample(channel, dry + (wet * currentFeedback));
            
            channelData[sample] = dry * (1.0f - currentMix) + wet * currentMix;
        }
    }
}

void DelayProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    auto state = parameters->copyState();
    std::unique_ptr<juce::XmlElement> xml(state.createXml());
    copyXmlToBinary(*xml, destData);
}

void DelayProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    std::unique_ptr<juce::XmlElement> xmlState(getXmlFromBinary(data, sizeInBytes));
    if (xmlState.get() != nullptr)
        parameters->replaceState(juce::ValueTree::fromXml(*xmlState));
}