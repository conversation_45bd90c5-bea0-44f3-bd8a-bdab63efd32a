#pragma once

#include <JuceHeader.h>
#include "ModuleManager.h"

class ModuleUIManager
{
public:
    ModuleUIManager(ModuleManager& manager);
    ~ModuleUIManager();

    // UI Component creation methods
    std::unique_ptr<juce::Component> createComponentForProcessor(juce::AudioProcessor* processor);
    
    // Layout methods
    void layoutModules(juce::Component* parentComponent);
    void positionModule(ModuleUI* moduleUI, int& currentX, int& currentY, 
                        int& rowMaxHeight, int containerWidth);
    
    // Adding component to containers
    void addComponentToContainer(ModuleUI* moduleUI);
    
    // UI creation for modules
    std::unique_ptr<ModuleUI> createModuleUI(juce::AudioProcessor* processor, 
                                            const juce::String& moduleType,
                                            const juce::String& moduleName,
                                            juce::AudioProcessorGraph::NodeID nodeId);

private:
    ModuleManager& moduleManager;
    juce::Component* moduleUIsContainer = nullptr;
    int margin = 10; // Margin between modules

    // Component factories map
    std::map<juce::String, ComponentFactory> componentFactories;

}; 