﻿#pragma once

#include <JuceHeader.h>
#include "AudioGraphSnapshot.h"
#include "ModuleManager.h"

// Forward declarations
class ModuleManager;

class AudioSettingsWindow : public juce::DocumentWindow
{
public:
    AudioSettingsWindow(juce::AudioDeviceManager& deviceManager, ModuleManager* moduleManagerPtr = nullptr)
        : DocumentWindow(JUCE_T("Audio Settings"),
                        juce::LookAndFeel::getDefaultLookAndFeel().findColour(juce::ResizableWindow::backgroundColourId),
                        DocumentWindow::closeButton,
                        true),
          deviceManager(deviceManager),
          moduleManager(moduleManagerPtr)
    {
        jassert(&deviceManager != nullptr);

        // Save current audio device settings
        if (auto* device = deviceManager.getCurrentAudioDevice())
        {
            oldSampleRate = device->getCurrentSampleRate();
            oldBufferSize = device->getCurrentBufferSizeSamples();

            DBG("AudioSettingsWindow - Saving current settings: SR=" +
                juce::String(oldSampleRate) + "Hz, BS=" + juce::String(oldBufferSize));

            // Save module state if ModuleManager is provided
            if (moduleManager != nullptr)
            {
                moduleManager->takeAudioGraphSnapshot(snapshot);
                DBG("Saved current module state with " + juce::String(snapshot.modules.size()) + " modules");
            }
        }

        // 确保Windows Audio (Exclusive Mode)是默认选择
        setExclusiveModeAsDefault();
        
        setUsingNativeTitleBar(true);
        setAlwaysOnTop(true);

        // 使用ScopedPointer管理选择器和容器的生命周期
        containerComponent = new juce::Component();
        
        // 创建音频设备选择器
        audioDeviceSelector = new juce::AudioDeviceSelectorComponent(
            deviceManager,
            0,      // min input channels
            256,    // max input channels
            0,      // min output channels
            256,    // max output channels
            true,   // show MIDI input options
            true,   // show MIDI output options
            false,  // show channel count options
            false   // show sample rate options
        );

        jassert(audioDeviceSelector != nullptr);
        
        // 尝试在UI创建后设置默认设备类型选择
        juce::Timer::callAfterDelay(100, [this]() {
            setExclusiveModeAsDefaultInUI();
        });

        // 创建应用按钮
        applyButton = new juce::TextButton("Apply Settings");
        applyButton->onClick = [this]() { applyButtonClicked(); };

        // 添加组件到容器
        containerComponent->addAndMakeVisible(audioDeviceSelector);
        containerComponent->addAndMakeVisible(applyButton);

        // 设置布局
        containerComponent->setSize(500, 450); // 增加高度以容纳按钮
        audioDeviceSelector->setBounds(0, 0, 500, 400);
        applyButton->setBounds(200, 410, 100, 30);

        setContentOwned(containerComponent, true);
        setResizable(true, true);
        centreWithSize(500, 450);

        // Ensure window is within screen bounds
        setBounds(getBounds().constrainedWithin(juce::Desktop::getInstance().getDisplays().getPrimaryDisplay()->userArea));

        DBG("AudioSettingsWindow constructed successfully");
    }

    ~AudioSettingsWindow() override
    {
        DBG("AudioSettingsWindow destructor called");
        
        // 在析构函数开始时清除所有引用，防止循环引用和悬挂指针
        if (containerComponent != nullptr)
        {
            // 确保在删除容器前先移除所有子组件
            if (audioDeviceSelector != nullptr)
            {
                containerComponent->removeChildComponent(audioDeviceSelector);
                delete audioDeviceSelector;
                audioDeviceSelector = nullptr;
            }
            
            if (applyButton != nullptr)
            {
                containerComponent->removeChildComponent(applyButton);
                delete applyButton;
                applyButton = nullptr;
            }
            
            // 清空容器内容
            containerComponent->removeAllChildren();
        }
        
        // 确保moduleManager指针安全
        moduleManager = nullptr;
    }

    void applyButtonClicked()
    {
        // 获取新的音频设置
        auto* device = deviceManager.getCurrentAudioDevice();
        if (device != nullptr && moduleManager != nullptr)
        {
            double newSampleRate = device->getCurrentSampleRate();
            int newBufferSize = device->getCurrentBufferSizeSamples();

            // 检查设置是否改变
            if (std::abs(newSampleRate - oldSampleRate) > 0.01 || newBufferSize != oldBufferSize)
            {
                DBG("Audio settings changed from SR=" + juce::String(oldSampleRate) +
                    "Hz, BS=" + juce::String(oldBufferSize) + " to SR=" +
                    juce::String(newSampleRate) + "Hz, BS=" + juce::String(newBufferSize));

                // 通知ModuleManager处理音频设置变更
                moduleManager->handleAudioSettingsChange(device);

                // 更新保存的设置
                oldSampleRate = newSampleRate;
                oldBufferSize = newBufferSize;

                DBG("Audio settings applied successfully");
            }
            else
            {
                DBG("Audio settings unchanged, no update needed");
            }
        }
    }

    void closeButtonPressed() override
    {
        DBG("AudioSettingsWindow::closeButtonPressed");

        // 确保在关闭窗口前明确清理子组件，避免内存泄漏
        if (containerComponent != nullptr)
        {
            containerComponent->removeAllChildren();
        }

        // 隐藏窗口
        setVisible(false);

        // 如果这是一个模态窗口，退出模态状态
        if (isCurrentlyModal())
        {
            exitModalState(0);
        }
    }

    static void showAudioSettings(juce::AudioDeviceManager& deviceManager, ModuleManager* moduleManager = nullptr)
    {
        // 为保持窗口生命周期，使用堆分配
        AudioSettingsWindow* window = new AudioSettingsWindow(deviceManager, moduleManager);
        
        // 使用自定义回调函数，确保在模态状态结束时正确清理资源
        window->enterModalState(true, 
                              juce::ModalCallbackFunction::create([](int) {
                                  // 此回调在模态窗口关闭时执行
                                  // 因为设置了deleteWhenDismissed=true，不需要手动删除窗口
                              }), 
                              true); // true表示在关闭时删除窗口
    }

    // Added: Initialize audio device manager
    static bool initializeAudioDeviceManager(juce::AudioDeviceManager& deviceManager,
                                           juce::MidiMessageCollector* midiCollector = nullptr,
                                           juce::MidiInputCallback* callback = nullptr)
    {
        // 设置Windows Audio (Exclusive Mode)作为默认设备类型
        // 直接设置设备类型而不复制OwnedArray
        const juce::String exclusiveModeTypeName = "Windows Audio (Exclusive Mode)";
        
        // 检查是否有这个设备类型
        bool hasExclusiveMode = false;
        for (auto* type : deviceManager.getAvailableDeviceTypes())
        {
            if (type->getTypeName() == exclusiveModeTypeName)
            {
                hasExclusiveMode = true;
                // 设置为当前设备类型
                deviceManager.setCurrentAudioDeviceType(exclusiveModeTypeName, true);
                DBG("Set audio device type to: " + exclusiveModeTypeName);
                break;
            }
        }
        
        // 获取当前设置
        juce::AudioDeviceManager::AudioDeviceSetup deviceSetup;
        deviceManager.getAudioDeviceSetup(deviceSetup);
        
        // 设置为Windows Audio (Exclusive Mode) - 第二个选项        
        deviceSetup.useDefaultOutputChannels = true;
        deviceSetup.useDefaultInputChannels = true;
        deviceSetup.sampleRate = 44100;
        deviceSetup.bufferSize = 512;
        
        // 应用设置
        auto error = deviceManager.setAudioDeviceSetup(deviceSetup, true);
        
        // 如果设置失败，尝试初始化默认设置
        if (error.isNotEmpty()) {
            DBG("Failed to set audio device setup: " + error);
            
            error = deviceManager.initialise(
                0,      // input channels
                2,      // output channels
                nullptr,// XML settings
                true,   // select default device
                juce::String(),  // preferred device name
                nullptr  // preferred setup options
            );
        }

        if (error.isNotEmpty())
        {
            DBG("Device initialization error: " + error);
            return false;
        }

        DBG("Audio device initialized successfully");
        if (auto* device = deviceManager.getCurrentAudioDevice())
        {
            juce::String deviceName = device->getName();
            if (deviceName.isEmpty())
                deviceName = "Unnamed Device";

            DBG("Using device: " + deviceName);
            DBG("Using device type: " + deviceManager.getCurrentAudioDeviceType());
            DBG("Sample rate: " + juce::String(device->getCurrentSampleRate()));
            DBG("Buffer size: " + juce::String(device->getCurrentBufferSizeSamples()));
        }

        // Initialize MIDI devices
        DBG("Initializing MIDI devices...");
        auto midiInputs = juce::MidiInput::getAvailableDevices();
        if (midiInputs.isEmpty())
        {
            DBG("No MIDI input devices found!");
        }
        else
        {
            DBG("Available MIDI input devices:");
            for (const auto& input : midiInputs)
            {
                juce::String inputName = input.name;
                if (inputName.isEmpty())
                    inputName = "Unnamed MIDI Device(" + input.identifier + ")";

                DBG(" - " + inputName);

                // Enable all MIDI devices by default
                deviceManager.setMidiInputDeviceEnabled(input.identifier, true);
                DBG("   Enabled MIDI device: " + inputName);

                // Add callback
                if (callback != nullptr)
                {
                    deviceManager.addMidiInputDeviceCallback(input.identifier, callback);
                }

                // Add MidiMessageCollector as callback
                if (midiCollector != nullptr)
                {
                    deviceManager.addMidiInputDeviceCallback(input.identifier, midiCollector);
                    DBG("   Added MIDI callbacks for: " + inputName);
                }
            }
        }

        return true;
    }

    // Added: Cleanup MIDI device callbacks
    static void cleanupMidiDevices(juce::AudioDeviceManager& deviceManager,
                                 juce::MidiMessageCollector* midiCollector = nullptr,
                                 juce::MidiInputCallback* callback = nullptr)
    {
        auto midiInputs = juce::MidiInput::getAvailableDevices();
        for (const auto& input : midiInputs)
        {
            juce::String inputName = input.name;
            if (inputName.isEmpty())
                inputName = "Unnamed MIDI Device(" + input.identifier + ")";

            if (callback != nullptr)
            {
                deviceManager.removeMidiInputDeviceCallback(input.identifier, callback);
            }

            if (midiCollector != nullptr)
            {
                deviceManager.removeMidiInputDeviceCallback(input.identifier, midiCollector);
            }

            DBG("Released MIDI callbacks for: " + inputName);
        }
    }

    // 新增方法：尝试将Windows Audio (Exclusive Mode)设置为默认设备类型
    void setExclusiveModeAsDefault()
    {
        const juce::String exclusiveModeTypeName = "Windows Audio (Exclusive Mode)";
        
        // 检查是否有这个设备类型并设置它
        for (auto* type : deviceManager.getAvailableDeviceTypes())
        {
            if (type->getTypeName() == exclusiveModeTypeName)
            {
                DBG("Setting default audio device type to: " + exclusiveModeTypeName);
                deviceManager.setCurrentAudioDeviceType(exclusiveModeTypeName, false);
                break;
            }
        }
    }
    
    // 尝试在UI中选择Windows Audio (Exclusive Mode)
    void setExclusiveModeAsDefaultInUI()
    {
        // AudioDeviceSelectorComponent没有公开其内部ComboBox的API
        // 但它在其构造后状态应该已经反映了deviceManager的设置
        // 确保deviceManager已经设置了正确的类型
        const juce::String exclusiveModeTypeName = "Windows Audio (Exclusive Mode)";
        
        if (deviceManager.getCurrentAudioDeviceType() != exclusiveModeTypeName) {
            // 再次尝试设置设备类型
            setExclusiveModeAsDefault();
            
            // 强制AudioDeviceSelectorComponent刷新
            if (audioDeviceSelector != nullptr) {
                // AudioDeviceSelectorComponent没有updateAllControls方法
                // 我们只能重建整个组件
                containerComponent->removeChildComponent(audioDeviceSelector);
                delete audioDeviceSelector;
                
                audioDeviceSelector = new juce::AudioDeviceSelectorComponent(
                    deviceManager,
                    0,      // min input channels
                    256,    // max input channels
                    0,      // min output channels
                    256,    // max output channels
                    true,   // show MIDI input options
                    true,   // show MIDI output options
                    false,  // show channel count options
                    false   // show sample rate options
                );
                
                containerComponent->addAndMakeVisible(audioDeviceSelector);
                audioDeviceSelector->setBounds(0, 0, 500, 400);
                
                // 重建参数附件
                // 这里不需要处理，因为设备选择器不使用ValueTree参数
            }
            
            DBG("Rebuilt AudioDeviceSelectorComponent to show Windows Audio (Exclusive Mode)");
        }
    }

private:
    juce::AudioDeviceManager& deviceManager;
    ModuleManager* moduleManager = nullptr;
    AudioGraphSnapshot snapshot;
    double oldSampleRate = 44100.0;
    int oldBufferSize = 512;
    
    // 使用指针成员来明确管理组件生命周期
    juce::Component* containerComponent = nullptr;
    juce::AudioDeviceSelectorComponent* audioDeviceSelector = nullptr;
    juce::TextButton* applyButton = nullptr;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioSettingsWindow)
};