#include "ModuleConnectionManager.h"
#include "ModuleManager.h"

ModuleConnectionManager::ModuleConnectionManager(ModuleManager& manager)
    : moduleManager(manager)
{
}

ModuleConnectionManager::~ModuleConnectionManager()
{
    // No specific cleanup needed
}

bool ModuleConnectionManager::processConnectionCommand(const juce::String& command)
{
    // Parse connection command, e.g. "c osc1 0 aou1 0"
    auto parts = juce::StringArray::fromTokens(command, " ", "");
    
    // Check if format is correct
    if (parts.size() < 5 || parts[0] != "c")
    {
        DBG("Invalid connection command format: " + command);
        return false;
    }
    
    // Parse parameters
    juce::String sourceModuleName = parts[1];
    int sourceChannelIndex = parts[2].getIntValue();
    juce::String destModuleName = parts[3];
    int destChannelIndex = parts[4].getIntValue();
    
    // Find source and destination modules using ModuleManager's helper method
    auto sourceNodeId = moduleManager.findNodeIdForName(sourceModuleName);
    auto destNodeId = moduleManager.findNodeIdForName(destModuleName);
    
    // Check if nodes were found
    if (!sourceNodeId.uid || !destNodeId.uid)
    {
        DBG("Cannot find source or destination module: " + sourceModuleName + " -> " + destModuleName);
        return false;
    }
    
    // Create connection
    return connectProcessors(sourceNodeId, sourceChannelIndex, destNodeId, destChannelIndex);
}

bool ModuleConnectionManager::connectProcessors(juce::AudioProcessorGraph::NodeID sourceNodeId, 
                                              int sourceChannelIndex,
                                              juce::AudioProcessorGraph::NodeID destNodeId, 
                                              int destChannelIndex)
{
    bool result = false;
    
    if (moduleManager.getModuleSystem() != nullptr)
    {
        // Create the connection using the ModuleSystemComponent
        result = moduleManager.getModuleSystem()->connectProcessors(sourceNodeId, sourceChannelIndex, 
                                                             destNodeId, destChannelIndex);
        
        // Get module names for debug output using ModuleManager's helper
        juce::String sourceName = moduleManager.getModuleNameForNodeId(sourceNodeId);
        juce::String destName = moduleManager.getModuleNameForNodeId(destNodeId);
        
        if (result)
        {
            DBG("Created connection: " + sourceName + " (channel " + juce::String(sourceChannelIndex) + 
                ") -> " + destName + " (channel " + juce::String(destChannelIndex) + ")");
        }
        else
        {
            DBG("Failed to create connection: " + sourceName + " (channel " + juce::String(sourceChannelIndex) + 
                ") -> " + destName + " (channel " + juce::String(destChannelIndex) + ")");
        }
    }
    
    return result;
}

bool ModuleConnectionManager::connectMidiProcessors(juce::AudioProcessorGraph::NodeID sourceNodeId, 
                                                  juce::AudioProcessorGraph::NodeID destNodeId)
{
    bool result = false;
    
    if (moduleManager.getModuleSystem() != nullptr)
    {
        // Use MIDI connection in the module system
        result = moduleManager.getModuleSystem()->connectMidiProcessors(sourceNodeId, destNodeId);
        
        // Get module names for debug output using ModuleManager's helper
        juce::String sourceName = moduleManager.getModuleNameForNodeId(sourceNodeId);
        juce::String destName = moduleManager.getModuleNameForNodeId(destNodeId);
        
        if (result)
        {
            DBG("Created MIDI connection: " + sourceName + " -> " + destName);
        }
        else
        {
            DBG("Failed to create MIDI connection: " + sourceName + " -> " + destName);
        }
    }
    
    return result;
}

void ModuleConnectionManager::takeConnectionSnapshot(AudioGraphSnapshot& snapshot)
{
    // This only deals with the connections part of the snapshot
    if (moduleManager.getModuleSystem() == nullptr)
        return;
        
    // Get all connections
    auto connections = moduleManager.getModuleSystem()->getConnections();
    
    for (const auto& connection : connections)
    {
        // Find source and destination module names using ModuleManager's helper
        juce::String sourceName = moduleManager.getModuleNameForNodeId(connection.source.nodeID);
        juce::String destName = moduleManager.getModuleNameForNodeId(connection.destination.nodeID);
        
        // If module names were found, add connection (check they're not default Node+ID names)
        if (sourceName.isNotEmpty() && destName.isNotEmpty() && 
            sourceName != "Node" + juce::String(connection.source.nodeID.uid) && 
            destName != "Node" + juce::String(connection.destination.nodeID.uid))
        {
            AudioGraphSnapshot::ConnectionInfo connInfo;
            connInfo.sourceModule = sourceName;
            connInfo.sourcePort = connection.source.channelIndex;
            connInfo.destModule = destName;
            connInfo.destPort = connection.destination.channelIndex;
            
            // Determine connection type
            juce::AudioProcessor* sourceProcessor = moduleManager.getModuleSystem()->getProcessorForNode(connection.source.nodeID);
            if (sourceProcessor != nullptr && sourceProcessor->producesMidi())
            {
                connInfo.isMidiConnection = true;
                DBG("Added MIDI connection to snapshot: " + sourceName + ":" + 
                    juce::String(connection.source.channelIndex) + " -> " + 
                    destName + ":" + juce::String(connection.destination.channelIndex));
            }
            else
            {
                connInfo.isMidiConnection = false;
                DBG("Added Audio connection to snapshot: " + sourceName + ":" + 
                    juce::String(connection.source.channelIndex) + " -> " + 
                    destName + ":" + juce::String(connection.destination.channelIndex));
            }
            
            snapshot.connections.push_back(connInfo);
        }
    }
}

void ModuleConnectionManager::restoreConnections(const AudioGraphSnapshot& snapshot, 
                                               const std::map<juce::String, juce::AudioProcessorGraph::NodeID>& moduleMap)
{
    for (const auto& connection : snapshot.connections)
    {
        // Get source and destination node IDs
        auto sourceIt = moduleMap.find(connection.sourceModule);
        auto destIt = moduleMap.find(connection.destModule);
        
        if (sourceIt != moduleMap.end() && destIt != moduleMap.end())
        {
            auto sourceNodeId = sourceIt->second;
            auto destNodeId = destIt->second;
            
            bool result;
            if (connection.isMidiConnection)
            {
                // Create MIDI connection
                result = connectMidiProcessors(sourceNodeId, destNodeId);
            }
            else
            {
                // Create audio connection
                result = connectProcessors(sourceNodeId, connection.sourcePort, 
                                          destNodeId, connection.destPort);
            }
            
            if (!result)
            {
                DBG("Failed to restore connection: " + connection.sourceModule + " -> " + connection.destModule);
            }
        }
        else
        {
            DBG("Cannot find source or destination module for connection");
        }
    }
}

juce::String ModuleConnectionManager::getConnectionsState() const
{
    juce::String result;
    
    // Add connection information
    if (moduleManager.getModuleSystem() != nullptr)
    {
        auto connections = moduleManager.getModuleSystem()->getConnections();
        result += "Connections (" + juce::String(connections.size()) + "):\n";
        
        for (const auto& connection : connections)
        {
            // Find source and destination module names using ModuleManager's helper method
            juce::String sourceName = moduleManager.getModuleNameForNodeId(connection.source.nodeID);
            juce::String destName = moduleManager.getModuleNameForNodeId(connection.destination.nodeID);
            
            result += "  " + sourceName + " (ch " + juce::String(connection.source.channelIndex) + 
                    ") -> " + destName + " (ch " + juce::String(connection.destination.channelIndex) + ")\n";
        }
    }
    
    return result;
} 