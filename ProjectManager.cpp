﻿#include "ProjectManager.h"
#include <JuceHeader.h>

#include <memory>

// 使用JUCE宏屏蔽MSVC警告
JUCE_BEGIN_IGNORE_WARNINGS_MSVC(4100 4244 4996)

// Initialize static Identifiers
const juce::Identifier ProjectManager::Ids::PROJECT("SynthProject");
const juce::Identifier ProjectManager::Ids::VERSION("version");
const juce::Identifier ProjectManager::Ids::MODULES("Modules");
const juce::Identifier ProjectManager::Ids::MODULE("Module");
const juce::Identifier ProjectManager::Ids::CONNECTIONS("Connections");
const juce::Identifier ProjectManager::Ids::CONNECTION("Connection");
const juce::Identifier ProjectManager::Ids::TYPE("type");
const juce::Identifier ProjectManager::Ids::NAME("name");
const juce::Identifier ProjectManager::Ids::STATE("state");
const juce::Identifier ProjectManager::Ids::SOURCE_MODULE("sourceModule");
const juce::Identifier ProjectManager::Ids::SOURCE_PORT("sourcePort");
const juce::Identifier ProjectManager::Ids::DEST_MODULE("destModule");
const juce::Identifier ProjectManager::Ids::DEST_PORT("destPort");
const juce::Identifier ProjectManager::Ids::IS_MIDI("isMidi");

ProjectManager::ProjectManager(ModuleManager& manager)
    : moduleManager(manager)
{
}

bool ProjectManager::saveProject(const juce::File& file)
{
    DBG("ProjectManager::saveProject - Saving to file: " + file.getFullPathName());
    DBG("File exists: " + juce::String(file.exists() ? "yes" : "no"));
    DBG("File is file: " + juce::String(file.existsAsFile() ? "yes" : "no"));
    DBG("File is directory: " + juce::String(file.isDirectory() ? "yes" : "no"));

    // Create project tree
    juce::ValueTree projectTree = createProjectTree();
    DBG("Created project tree with " + juce::String(projectTree.getNumChildren()) + " children");

    // Save to file
    if (file.hasFileExtension(".xml"))
    {
        DBG("Saving as XML format");
        // Save as XML format
        std::unique_ptr<juce::XmlElement> xml(projectTree.createXml());
        if (xml != nullptr)
        {
            bool success = xml->writeTo(file);
            DBG("XML write result: " + juce::String(success ? "success" : "failed"));

            // 检查文件是否真的被创建
            if (success && !file.existsAsFile())
            {
                DBG("Warning: File reported as saved but doesn't exist: " + file.getFullPathName());
                return false;
            }

            return success;
        }
        else
        {
            DBG("Failed to create XML from ValueTree");
        }
    }
    else
    {
        DBG("Saving as binary format");

        // 创建文件输出流
        juce::FileOutputStream stream(file);
        
        if (stream.openedOk())
        {
            DBG("File stream opened successfully");
            projectTree.writeToStream(stream);
            stream.flush();
            bool streamOk = stream.getStatus().wasOk();
            DBG("Stream status after write: " + juce::String(streamOk ? "OK" : "Failed"));

            // 流会在这里自动关闭（析构函数中）

            if (!streamOk)
            {
                DBG("Stream reported error: " + stream.getStatus().getErrorMessage());
                return false;
            }

            // 检查文件是否真的被创建
            if (!file.existsAsFile())
            {
                DBG("Warning: File reported as saved but doesn't exist: " + file.getFullPathName());
                return false;
            }

            DBG("Binary write successful");
            return true;
        }
        else
        {
            DBG("Failed to open FileOutputStream for file: " + file.getFullPathName());
            DBG("Error: " + stream.getStatus().getErrorMessage());
            return false;
        }
    }

    DBG("Save operation failed");
    return false;
}

bool ProjectManager::loadProject(const juce::File& file)
{
    // Clear current project
    clearProject();

    // Load ValueTree from file
    juce::ValueTree projectTree;

    if (file.hasFileExtension(".xml"))
    {
        // Load from XML
        juce::XmlDocument xmlDoc(file);
        std::unique_ptr<juce::XmlElement> xml(xmlDoc.getDocumentElement());
        if (xml != nullptr && xml->hasTagName(Ids::PROJECT.toString()))
        {
            projectTree = juce::ValueTree::fromXml(*xml);
        }
    }
    else
    {
        // Load from binary
        juce::FileInputStream stream(file);
        if (stream.openedOk())
        {
            projectTree = juce::ValueTree::readFromStream(stream);
        }
    }

    // Validate project tree
    if (!projectTree.isValid() || projectTree.getType() != Ids::PROJECT)
    {
        DBG("Error: Invalid project file");
        return false;
    }

    // Check version compatibility
    int version = projectTree.getProperty(Ids::VERSION, 1);
    DBG("Loading project version: " + juce::String(version));

    // Restore modules
    if (projectTree.getNumChildren() > 0 && projectTree.getChild(0).getType() == Ids::MODULES)
    {
        if (!restoreModules(projectTree.getChild(0)))
        {
            DBG("Error: Failed to restore modules");
            return false;
        }
    }

    // Restore connections
    if (projectTree.getNumChildren() > 1 && projectTree.getChild(1).getType() == Ids::CONNECTIONS)
    {
        if (!restoreConnections(projectTree.getChild(1)))
        {
            DBG("Error: Failed to restore connections");
            return false;
        }
    }

    return true;
}

juce::ValueTree ProjectManager::createProjectTree()
{
    // Create root node
    juce::ValueTree projectTree(Ids::PROJECT);
    projectTree.setProperty(Ids::VERSION, 1, nullptr);

    // Add modules and connections
    juce::ValueTree modulesTree(Ids::MODULES);
    saveModules(modulesTree);
    projectTree.addChild(modulesTree, -1, nullptr);

    juce::ValueTree connectionsTree(Ids::CONNECTIONS);
    saveConnections(connectionsTree);
    projectTree.addChild(connectionsTree, -1, nullptr);

    return projectTree;
}

void ProjectManager::saveModules(juce::ValueTree& parentTree)
{
    // Get all modules
    const auto& moduleUIs = moduleManager.getModuleUIs();
    auto* moduleSystem = moduleManager.getModuleSystem();

    for (const auto& moduleUI : moduleUIs)
    {
        // Create module node
        juce::ValueTree moduleTree(Ids::MODULE);
        moduleTree.setProperty(Ids::TYPE, moduleUI->type, nullptr);
        moduleTree.setProperty(Ids::NAME, moduleUI->uniqueName, nullptr);

        // Get module state
        auto* processor = moduleSystem->getProcessorForNode(moduleUI->nodeId);
        if (processor != nullptr)
        {
            juce::MemoryBlock stateData;
            processor->getStateInformation(stateData);

            if (stateData.getSize() > 0)
            {
                moduleTree.setProperty(Ids::STATE, stateData.toBase64Encoding(), nullptr);
            }
        }

        // Add to parent node
        parentTree.addChild(moduleTree, -1, nullptr);
    }
}

void ProjectManager::saveConnections(juce::ValueTree& parentTree)
{
    // Get audio graph
    auto* moduleSystem = moduleManager.getModuleSystem();
    if (moduleSystem == nullptr)
        return;

    // Get all connections
    auto audioGraph = moduleManager.getAudioGraph();
    if (audioGraph == nullptr)
        return;

    auto connections = audioGraph->getConnections();

    // Create mapping from module ID to name
    std::map<juce::AudioProcessorGraph::NodeID, juce::String> nodeIdToName;
    for (const auto& moduleUI : moduleManager.getModuleUIs())
    {
        nodeIdToName[moduleUI->nodeId] = moduleUI->uniqueName;
    }

    // Save each connection
    for (const auto& connection : connections)
    {
        // Check if source and destination nodes are in the mapping
        if (nodeIdToName.find(connection.source.nodeID) == nodeIdToName.end() ||
            nodeIdToName.find(connection.destination.nodeID) == nodeIdToName.end())
        {
            continue; // Skip connections with unknown nodes
        }

        // Create connection node
        juce::ValueTree connectionTree(Ids::CONNECTION);

        // Set connection properties
        connectionTree.setProperty(Ids::SOURCE_MODULE, nodeIdToName[connection.source.nodeID], nullptr);
        connectionTree.setProperty(Ids::SOURCE_PORT, connection.source.channelIndex, nullptr);
        connectionTree.setProperty(Ids::DEST_MODULE, nodeIdToName[connection.destination.nodeID], nullptr);
        connectionTree.setProperty(Ids::DEST_PORT, connection.destination.channelIndex, nullptr);

        // Determine if it's a MIDI connection
        bool isMidi = (connection.source.channelIndex == juce::AudioProcessorGraph::midiChannelIndex &&
                      connection.destination.channelIndex == juce::AudioProcessorGraph::midiChannelIndex);
        connectionTree.setProperty(Ids::IS_MIDI, isMidi, nullptr);

        // Add to parent node
        parentTree.addChild(connectionTree, -1, nullptr);
    }
}

bool ProjectManager::restoreModules(const juce::ValueTree& modulesTree)
{
    // Check if it's a valid modules tree
    if (modulesTree.getType() != Ids::MODULES)
        return false;

    // Restore each module
    for (int i = 0; i < modulesTree.getNumChildren(); ++i)
    {
        auto moduleTree = modulesTree.getChild(i);

        if (moduleTree.getType() != Ids::MODULE)
            continue;

        // Get module type and name
        juce::String type = moduleTree.getProperty(Ids::TYPE);
        juce::String name = moduleTree.getProperty(Ids::NAME);

        // Create module
        moduleManager.createModuleFromName(type + " " + name);

        // Restore module state
        if (moduleTree.hasProperty(Ids::STATE))
        {
            juce::String stateBase64 = moduleTree.getProperty(Ids::STATE);
            juce::MemoryBlock stateData;

            if (stateData.fromBase64Encoding(stateBase64))
            {
                // Find module
                juce::AudioProcessor* processor = moduleManager.findModuleByName(name);
                if (processor != nullptr)
                {
                    processor->setStateInformation(stateData.getData(), static_cast<int>(stateData.getSize()));
                }
            }
        }
    }

    return true;
}

bool ProjectManager::restoreConnections(const juce::ValueTree& connectionsTree)
{
    // Check if it's a valid connections tree
    if (connectionsTree.getType() != Ids::CONNECTIONS)
        return false;

    // Restore each connection
    for (int i = 0; i < connectionsTree.getNumChildren(); ++i)
    {
        auto connectionTree = connectionsTree.getChild(i);

        if (connectionTree.getType() != Ids::CONNECTION)
            continue;

        // Get connection properties
        juce::String sourceModule = connectionTree.getProperty(Ids::SOURCE_MODULE);
        int sourcePort = connectionTree.getProperty(Ids::SOURCE_PORT);
        juce::String destModule = connectionTree.getProperty(Ids::DEST_MODULE);
        int destPort = connectionTree.getProperty(Ids::DEST_PORT);
        bool isMidi = connectionTree.getProperty(Ids::IS_MIDI, false);

        // Find modules
        juce::AudioProcessorGraph::NodeID sourceNodeId = moduleManager.findNodeIdForName(sourceModule);
        juce::AudioProcessorGraph::NodeID destNodeId = moduleManager.findNodeIdForName(destModule);

        if (sourceNodeId.uid == 0 || destNodeId.uid == 0)
            continue;

        // Create connection
        if (isMidi)
        {
            // MIDI connection
            moduleManager.getModuleSystem()->connectMidiProcessors(sourceNodeId, destNodeId);
        }
        else
        {
            // Audio connection
            moduleManager.getModuleSystem()->connectProcessors(
                sourceNodeId, sourcePort,
                destNodeId, destPort);
        }
    }

    return true;
}

void ProjectManager::clearProject()
{
    // Get all modules
    const auto& moduleUIs = moduleManager.getModuleUIs();

    // Remove all modules (from back to front to avoid index issues)
    for (int i = moduleUIs.size() - 1; i >= 0; --i)
    {
        // 使用ModuleSystem的removeProcessor方法移除处理器
        moduleManager.getModuleSystem()->removeProcessor(moduleUIs[i]->nodeId);
    }
}

JUCE_END_IGNORE_WARNINGS_MSVC
