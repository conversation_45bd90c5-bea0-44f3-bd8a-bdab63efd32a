#pragma once

#include <JuceHeader.h>
#include "MixerProcessor.h"

//==============================================================================
class MixerProcessorComponent : public juce::AudioProcessorEditor,
                               public juce::Button::Listener
{
public:
    MixerProcessorComponent(MixerProcessor& p)
        : AudioProcessorEditor(&p), processor(p)
    {
        // Setup all controls
        setupControls();
        
        // Set component size
        setSize(200, 120);
    }
    
    ~MixerProcessorComponent() override
    {
        // Remove button listeners
        for (int i = 0; i < MixerProcessor::NUM_CHANNELS; ++i)
        {
            muteButtons[i].removeListener(this);
        }
        
        // Clean up all parameter attachments
        masterVolumeAttachment.reset();
        
        for (int i = 0; i < MixerProcessor::NUM_CHANNELS; ++i)
        {
            volumeAttachments[i].reset();
            panAttachments[i].reset();
            muteAttachments[i].reset();
        }
    }

    // Button::Listener implementation
    void buttonClicked(juce::Button* button) override
    {
        // Check if it's one of our mute buttons
        for (int i = 0; i < MixerProcessor::NUM_CHANNELS; ++i)
        {
            if (button == &muteButtons[i])
            {
                bool isOn = button->getToggleState();
                DBG("Button listener: Mute button " + juce::String(i) + " clicked - New state: " + 
                    juce::String(isOn ? "ON" : "OFF"));
                    
                // We don't need to manually set the parameter as the ButtonAttachment should do this
                // This is just for additional debugging
                juce::String paramId = "mute" + juce::String(i);
                DBG("Button listener: Parameter " + paramId + " should be updated to: " + (isOn ? "true" : "false"));
                
                break;
            }
        }
    }

    void paint(juce::Graphics& g) override
    {
        // Draw background
        g.fillAll(juce::Colours::darkgrey);
        
        // Draw border
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(1.0f), 5.0f, 1.0f);
        
        // Draw title
        g.setFont(15.0f);
        auto titleBounds = getLocalBounds().removeFromTop(20);
        g.drawText("Mixer", titleBounds.reduced(5), juce::Justification::centred);
        
        // Draw channel labels
        auto channelArea = getLocalBounds().reduced(5, 25);
        auto channelWidth = channelArea.getWidth() / MixerProcessor::NUM_CHANNELS;
        
        for (int i = 0; i < MixerProcessor::NUM_CHANNELS; ++i)
        {
            auto channelRect = channelArea.removeFromLeft(channelWidth).reduced(2);
            
            // Channel label
            g.setColour(juce::Colours::white);
            g.setFont(12.0f);
            g.drawText("CH" + juce::String(i + 1), channelRect.removeFromTop(15), juce::Justification::centred);
        }
    }

    void resized() override
    {
        // Main layout area
        auto area = getLocalBounds().reduced(5);
        area.removeFromTop(20); // Space for title
        
        // Master volume slider
        masterVolumeSlider.setBounds(area.removeFromBottom(20).reduced(5, 0));
        
        // Channel controls area
        auto channelsArea = area;
        auto channelWidth = channelsArea.getWidth() / MixerProcessor::NUM_CHANNELS;
        
        for (int i = 0; i < MixerProcessor::NUM_CHANNELS; ++i)
        {
            auto channelArea = channelsArea.removeFromLeft(channelWidth).reduced(2);
            
            // Space for channel label
            channelArea.removeFromTop(15);
            
            // Channel controls
            volumeSliders[i].setBounds(channelArea.removeFromTop(15).reduced(0, 1));
            panSliders[i].setBounds(channelArea.removeFromTop(15).reduced(0, 1));
            muteButtons[i].setBounds(channelArea.removeFromTop(15).reduced(5, 1));
        }
    }
    
private:
    // Setup all controls
    void setupControls()
    {
        DBG("MixerProcessorComponent: Setting up controls");
        
        // Setup master volume slider
        masterVolumeSlider.setSliderStyle(juce::Slider::LinearBar);
        masterVolumeSlider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 40, 15);
        masterVolumeSlider.setTextValueSuffix(" dB");
        addAndMakeVisible(masterVolumeSlider);
        
        auto& tree = processor.getParameterTree();
        masterVolumeAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
            tree, "masterVolume", masterVolumeSlider);
        
        // Setup all channel controls
        for (int i = 0; i < MixerProcessor::NUM_CHANNELS; ++i)
        {
            // Volume slider
            volumeSliders[i].setSliderStyle(juce::Slider::LinearBar);
            volumeSliders[i].setTextBoxStyle(juce::Slider::NoTextBox, false, 0, 0);
            volumeSliders[i].setPopupDisplayEnabled(true, true, this);
            volumeSliders[i].setTextValueSuffix(" dB");
            addAndMakeVisible(volumeSliders[i]);
            
            // Pan slider
            panSliders[i].setSliderStyle(juce::Slider::LinearBar);
            panSliders[i].setTextBoxStyle(juce::Slider::NoTextBox, false, 0, 0);
            panSliders[i].setPopupDisplayEnabled(true, true, this);
            addAndMakeVisible(panSliders[i]);
            
            // Mute button
            muteButtons[i].setButtonText("M");
            muteButtons[i].setColour(juce::TextButton::buttonOnColourId, juce::Colours::red);
            muteButtons[i].setClickingTogglesState(true); // Ensure it toggles
            muteButtons[i].addListener(this); // Add button listener for direct monitoring
            addAndMakeVisible(muteButtons[i]);
            
            // Create parameter attachments
            volumeAttachments[i] = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "volume" + juce::String(i), volumeSliders[i]);
                
            panAttachments[i] = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
                tree, "pan" + juce::String(i), panSliders[i]);
                
            juce::String muteParam = "mute" + juce::String(i);
            DBG("Setting up mute button " + muteParam);
            
            muteAttachments[i] = std::make_unique<juce::AudioProcessorValueTreeState::ButtonAttachment>(
                tree, muteParam, muteButtons[i]);
            
            DBG("Created attachment for " + muteParam);
        }
        
        // Dump all parameter values for debugging
        auto params = processor.getParameterTree().state;
        DBG("Current parameter values:");
        for (int i = 0; i < MixerProcessor::NUM_CHANNELS; ++i)
        {
            juce::String muteParam = "mute" + juce::String(i);
            juce::var value = params.getProperty(muteParam, false);
            DBG(muteParam + " = " + juce::String((float)value));
        }
        
        DBG("MixerProcessorComponent: Controls setup complete");
    }
    
    // Processor reference
    MixerProcessor& processor;
    
    // Master volume control
    juce::Slider masterVolumeSlider;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> masterVolumeAttachment;
    
    // Channel controls
    std::array<juce::Slider, MixerProcessor::NUM_CHANNELS> volumeSliders;
    std::array<juce::Slider, MixerProcessor::NUM_CHANNELS> panSliders;
    std::array<juce::TextButton, MixerProcessor::NUM_CHANNELS> muteButtons;
    
    // Parameter attachments
    std::array<std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment>, MixerProcessor::NUM_CHANNELS> volumeAttachments;
    std::array<std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment>, MixerProcessor::NUM_CHANNELS> panAttachments;
    std::array<std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment>, MixerProcessor::NUM_CHANNELS> muteAttachments;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MixerProcessorComponent)
}; 