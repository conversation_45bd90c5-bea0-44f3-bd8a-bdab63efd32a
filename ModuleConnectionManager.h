#pragma once

#include <JuceHeader.h>
#include "AudioGraphSnapshot.h"

// Forward declaration
struct ModuleUI;
class ModuleManager;

class ModuleConnectionManager
{
public:
    ModuleConnectionManager(ModuleManager& manager);
    ~ModuleConnectionManager();

    // Connection methods
    bool processConnectionCommand(const juce::String& command);
    bool connectProcessors(juce::AudioProcessorGraph::NodeID sourceNodeId, int sourceChannelIndex,
                         juce::AudioProcessorGraph::NodeID destNodeId, int destChannelIndex);
    bool connectMidiProcessors(juce::AudioProcessorGraph::NodeID sourceNodeId, 
                             juce::AudioProcessorGraph::NodeID destNodeId);
    
    // Snapshot methods
    void takeConnectionSnapshot(AudioGraphSnapshot& snapshot);
    void restoreConnections(const AudioGraphSnapshot& snapshot, 
                           const std::map<juce::String, juce::AudioProcessorGraph::NodeID>& moduleMap);
    
    // Get connections info
    juce::String getConnectionsState() const;

private:
    ModuleManager& moduleManager;
}; 