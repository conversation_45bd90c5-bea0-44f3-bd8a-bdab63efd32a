/*
  ==============================================================================

    ModulePresetManager.h
    Created: 2023
    Author:  Synth Project Team

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>

/**
 * Class for managing module presets stored in JSON format
 * Handles loading, saving, and retrieving preset configurations
 */
class ModulePresetManager
{
public:
    /** Constructor */
    ModulePresetManager()
    {
        // Initialize with empty presets structure
        presets = juce::var(juce::JSON::parse("{ \"commands\": [] }"));
    }
    
    /**
     * Load presets from a JSON file
     * @param presetFile File object pointing to the JSON file
     * @return True if loaded successfully
     */
    bool loadPresetsFromFile(const juce::File& presetFile)
    {
        if (!presetFile.existsAsFile())
        {
            DBG("Preset file does not exist: " + presetFile.getFullPathName());
            return false;
        }
        
        juce::String jsonContent = presetFile.loadFileAsString();
        auto json = juce::JSON::parse(jsonContent);
        
        if (!json.hasProperty("commands"))
        {
            DBG("Invalid preset file format: missing 'commands' array");
            return false;
        }
        
        presets = json;
        currentPresetFile = presetFile;
        DBG("Loaded " + juce::String(presets["commands"].getArray()->size()) + " presets from " 
            + presetFile.getFullPathName());
        
        return true;
    }
    
    /**
     * Check if a command exists in the loaded presets
     * @param commandName Name of the command to check
     * @return True if command exists
     */
    bool hasPresetCommand(const juce::String& commandName) const
    {
        if (auto* commands = presets["commands"].getArray())
        {
            for (const auto& command : *commands)
            {
                if (command["name"].toString().equalsIgnoreCase(commandName))
                    return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get configuration for a specific command
     * @param commandName Name of the command
     * @return Var object containing command configuration or empty var if not found
     */
    juce::var getCommandConfig(const juce::String& commandName) const
    {
        if (auto* commands = presets["commands"].getArray())
        {
            for (const auto& command : *commands)
            {
                if (command["name"].toString().equalsIgnoreCase(commandName))
                    return command;
            }
        }
        
        return {};
    }
    
    /**
     * Save a new preset to the current JSON file
     * @param commandName Name of the command
     * @param moduleTypes Array of module types to create
     * @param connections Array of connection configurations
     * @param description Optional description of the preset
     * @return True if saved successfully
     */
    bool savePresetToFile(const juce::String& commandName, 
                          const juce::Array<juce::String>& moduleTypes,
                          const juce::Array<juce::var>& connections,
                          const juce::String& description = "")
    {
        if (!currentPresetFile.existsAsFile())
        {
            DBG("No preset file has been loaded");
            return false;
        }
        
        // Create new command object
        juce::var newCommand(new juce::DynamicObject());
        newCommand.getDynamicObject()->setProperty("name", commandName);
        newCommand.getDynamicObject()->setProperty("description", description);
        
        // Add modules
        juce::Array<juce::var> modules;
        for (const auto& type : moduleTypes)
        {
            juce::var module(new juce::DynamicObject());
            module.getDynamicObject()->setProperty("shortName", type);
            module.getDynamicObject()->setProperty("type", type);
            modules.add(module);
        }
        newCommand.getDynamicObject()->setProperty("modules", modules);
        
        // Add connections
        newCommand.getDynamicObject()->setProperty("connections", connections);
        
        // Add or replace in presets collection
        auto* commands = presets["commands"].getArray();
        
        // Remove existing command with same name if it exists
        for (int i = 0; i < commands->size(); ++i)
        {
            if ((*commands)[i]["name"].toString().equalsIgnoreCase(commandName))
            {
                commands->remove(i);
                break;
            }
        }
        
        commands->add(newCommand);
        
        // Save to file
        juce::String jsonContent = juce::JSON::toString(presets, true); // Pretty-print JSON for readability
        bool success = currentPresetFile.replaceWithText(jsonContent);
        
        if (success)
            DBG("Saved preset '" + commandName + "' to " + currentPresetFile.getFullPathName());
        else
            DBG("Failed to save preset '" + commandName + "'");
        
        return success;
    }

private:
    juce::var presets;  // Holds the parsed JSON data
    juce::File currentPresetFile;  // Reference to the current preset file
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModulePresetManager)
}; 