﻿#pragma once

#include <JuceHeader.h>
#include "ADHSREnvelope.h"
#include <set>

//==============================================================================
class EnvelopeGeneratorProcessor : public juce::AudioProcessor,
                                  public juce::AudioProcessorValueTreeState::Listener
{
public:
    //==============================================================================
    EnvelopeGeneratorProcessor();
    ~EnvelopeGeneratorProcessor() override;
    
    //==============================================================================
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    
    void processBlock(juce::AudioBuffer<float>&, juce::MidiBuffer&) override;
    
    //==============================================================================
    juce::AudioProcessorEditor* createEditor() override;
    bool hasEditor() const override { return true; }
    
    //==============================================================================
    const juce::String getName() const override { return "Envelope Generator"; }
    
    // MIDI配置 - 明确支持MIDI输入
    bool acceptsMidi() const override { return true; }
    bool producesMidi() const override { return false; }
    bool isMidiEffect() const override { return false; }
    double getTailLengthSeconds() const override { return 0.0; }
    
    // 总线布局支持
    bool isBusesLayoutSupported(const BusesLayout& layouts) const override
    {
        // 支持立体声输入和输出
        if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo() ||
            layouts.getMainInputChannelSet() != juce::AudioChannelSet::stereo())
            return false;

        return true;
    }
    
    // MIDI通道控制
    int getMidiChannel() const { return midiChannel; }
    void setMidiChannel(int newChannel) { midiChannel = juce::jlimit(1, 16, newChannel); }
    
    // 添加MIDI连接相关方法
    void enableMidiInput(bool shouldEnable) { midiInputEnabled = shouldEnable; }
    bool isMidiInputEnabled() const { return midiInputEnabled; }
    void setMidiInputNodeId(juce::AudioProcessorGraph::NodeID nodeId) { midiInputNodeId = nodeId; }
    juce::AudioProcessorGraph::NodeID getMidiInputNodeId() const { return midiInputNodeId; }
    
    // 获取端口配置信息 - 用于帮助理解接口
    juce::String getPortConfiguration() const 
    { 
        return "Port 0: MIDI Input, Port 1: Stereo Audio Input, Port 2: Stereo Audio Output";
    }
    
    //==============================================================================
    int getNumPrograms() override { return 1; }
    int getCurrentProgram() override { return 0; }
    void setCurrentProgram(int index) override { juce::ignoreUnused(index); }
    const juce::String getProgramName(int index) override { juce::ignoreUnused(index); return {}; }
    void changeProgramName(int index, const juce::String& newName) override { juce::ignoreUnused(index, newName); }
    
    //==============================================================================
    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;
    
    //==============================================================================
    void parameterChanged(const juce::String& parameterID, float newValue) override;
    
    // 获取当前包络电平，用于可视化
    float getCurrentEnvelopeLevel() const { return currentEnvelopeLevel.load(); }
    
    // 获取左声道包络电平
    float getLeftChannelEnvelopeLevel() const { return leftChannelEnvelopeLevel.load(); }
    
    // 获取右声道包络电平
    float getRightChannelEnvelopeLevel() const { return rightChannelEnvelopeLevel.load(); }
    
    // 获取包络状态
    int getEnvelopeState() const { return static_cast<int>(envelope.getState()); }
    
    // 创建参数布局
    static juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout();
    
    // 参数树
    std::unique_ptr<juce::AudioProcessorValueTreeState> parameters = 
        std::make_unique<juce::AudioProcessorValueTreeState>(*this, nullptr, "Parameters", createParameterLayout());
    
    // 获取参数树（供UI组件使用）
    juce::AudioProcessorValueTreeState& getParameterTree() { return *parameters; }
    
private:
    // 更新包络参数
    void updateEnvelopeParameters();
    
    // 处理MIDI事件
    void handleMidiEvent(const juce::MidiMessage& message);
    
    // 包络生成器
    ADHSREnvelope envelope;
    
    // 样本率
    double sampleRate = 44100.0;
    
    // 平滑时间（毫秒）
    const float smoothingTimeMs = 50.0f;
    
    // 平滑处理后的参数
    juce::SmoothedValue<float> attackTimeSmoothed;
    juce::SmoothedValue<float> holdTimeSmoothed;
    juce::SmoothedValue<float> decayTimeSmoothed;
    juce::SmoothedValue<float> sustainLevelSmoothed;
    juce::SmoothedValue<float> releaseTimeSmoothed;
    
    // 原始参数值 (用于在参数变更时获取)
    std::atomic<float> attackTime { 0.1f };
    std::atomic<float> holdTime { 0.0f };
    std::atomic<float> decayTime { 0.3f };
    std::atomic<float> sustainLevel { 0.7f };
    std::atomic<float> releaseTime { 0.2f };
    
    // MIDI配置
    int midiChannel = 0;  // 0表示接收所有通道，1-16表示特定通道
    std::set<int> activeNotes;  // 当前活动的MIDI音符
    
    // 门控状态（音符按下状态）
    bool gateOn = false;
    
    // 当前包络电平（用于可视化）
    std::atomic<float> currentEnvelopeLevel { 0.0f };
    
    // 左声道包络电平
    std::atomic<float> leftChannelEnvelopeLevel { 0.0f };
    
    // 右声道包络电平
    std::atomic<float> rightChannelEnvelopeLevel { 0.0f };
    
    // 添加MIDI连接相关方法
    bool midiInputEnabled = false;
    juce::AudioProcessorGraph::NodeID midiInputNodeId; // 使用默认构造函数初始化
    
    //==============================================================================
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(EnvelopeGeneratorProcessor)
}; 