﻿#pragma once

#include <JuceHeader.h>

class MidiOutputProcessor : public juce::AudioProcessorGraph::AudioGraphIOProcessor
{
public:
    explicit MidiOutputProcessor(juce::AudioDeviceManager& deviceManager)
        : AudioGraphIOProcessor(juce::AudioProcessorGraph::AudioGraphIOProcessor::midiOutputNode),
          deviceManager(deviceManager)
    {
    }

    // 基本处理器功能
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages) override;
    
    // 状态管理
    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

    // UI相关
    juce::AudioProcessorEditor* createEditor() override { return nullptr; }
    bool hasEditor() const override { return false; }

    // 基本信息
    const juce::String getName() const override { return "MIDI Output"; }
    bool acceptsMidi() const override { return true; }
    bool producesMidi() const override { return false; }

    // MIDI通道控制
    int getMidiChannel() const { return midiChannel; }
    void setMidiChannel(int newChannel) { midiChannel = juce::jlimit(1, 16, newChannel); }

private:
    int midiChannel = 1;
    juce::AudioDeviceManager& deviceManager;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MidiOutputProcessor)
}; 