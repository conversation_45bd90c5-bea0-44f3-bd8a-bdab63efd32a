﻿#pragma once

#include <JuceHeader.h>
#include "EnvelopeGeneratorProcessor.h"

//==============================================================================
class EnvelopeGeneratorProcessorComponent : public juce::AudioProcessorEditor,
                                           private juce::Timer
{
public:
    EnvelopeGeneratorProcessorComponent(EnvelopeGeneratorProcessor& p)
        : AudioProcessorEditor(&p), processor(p)
    {
        // 创建和设置所有控件
        setupControls();
        
        // 启动定时器用于包络显示更新
        startTimerHz(30);
        
        // 设置组件大小与oscillator保持一致
        setSize(200, 120);
    }
    
    ~EnvelopeGeneratorProcessorComponent() override
    {
        stopTimer();
        
        // 清理滑块附件
        attackAttachment.reset();
        holdAttachment.reset();
        decayAttachment.reset();
        sustainAttachment.reset();
        releaseAttachment.reset();
    }

    void paint(juce::Graphics& g) override
    {
        // 绘制背景
        g.fillAll(juce::Colours::darkgrey);
        
        // 绘制边框
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(1.0f), 5.0f, 1.0f);
        
        // 绘制标题
        g.setFont(15.0f);
        auto titleBounds = getLocalBounds().removeFromTop(20);
        g.drawText("Envelope", titleBounds.reduced(5), juce::Justification::centred);
        
        // 绘制包络显示区域
        auto envelopeDisplayArea = getLocalBounds().reduced(10, 25).removeFromTop(40);
        g.setColour(juce::Colours::black);
        g.fillRect(envelopeDisplayArea);
        g.setColour(juce::Colours::white);
        g.drawRect(envelopeDisplayArea, 1.0f);
        
        // 绘制包络曲线
        drawEnvelopeCurve(g, envelopeDisplayArea);
        
        // 绘制当前包络电平指示 - 双声道版本
        float leftLevel = juce::jlimit(0.0f, 1.0f, processor.getLeftChannelEnvelopeLevel());
        float rightLevel = juce::jlimit(0.0f, 1.0f, processor.getRightChannelEnvelopeLevel());
        
        int leftLevelHeight = static_cast<int>(leftLevel * envelopeDisplayArea.getHeight());
        leftLevelHeight = juce::jmax(0, leftLevelHeight); // 保证非负
        
        int rightLevelHeight = static_cast<int>(rightLevel * envelopeDisplayArea.getHeight());
        rightLevelHeight = juce::jmax(0, rightLevelHeight); // 保证非负
        
        // 左声道音量条
        g.setColour(juce::Colours::lightgreen);
        g.fillRect(envelopeDisplayArea.getRight() - 10, 
                  envelopeDisplayArea.getBottom() - leftLevelHeight,
                  4, leftLevelHeight);
                  
        // 右声道音量条
        g.setColour(juce::Colours::lightblue);
        g.fillRect(envelopeDisplayArea.getRight() - 5, 
                  envelopeDisplayArea.getBottom() - rightLevelHeight,
                  4, rightLevelHeight);
    }

    void resized() override
    {
        // 布局区域
        auto area = getLocalBounds().reduced(5);
        area.removeFromTop(20); // 为标题留出空间
        
        // 包络显示区域
        auto envelopeDisplayArea = area.removeFromTop(40);
        area.removeFromTop(5); // 间距
        
        // 控制面板区域 - 改为2行3列的网格布局
        auto controlsArea = area;
        int controlHeight = 20;
        int controlGap = 3;
        
        // 第一行：Attack、Hold、Sustain
        auto topRow = controlsArea.removeFromTop(controlHeight);
        int columnWidth = topRow.getWidth() / 3;
        
        attackSlider.setBounds(topRow.removeFromLeft(columnWidth).reduced(1, 0));
        holdSlider.setBounds(topRow.removeFromLeft(columnWidth).reduced(1, 0));
        sustainSlider.setBounds(topRow.reduced(1, 0));
        
        controlsArea.removeFromTop(controlGap); // 行间距
        
        // 第二行：Decay、Release
        auto bottomRow = controlsArea.removeFromTop(controlHeight);
        columnWidth = bottomRow.getWidth() / 2;
        
        decaySlider.setBounds(bottomRow.removeFromLeft(columnWidth).reduced(1, 0));
        releaseSlider.setBounds(bottomRow.reduced(1, 0));
    }
    
    // 释放参数连接
    void releaseAttachments()
    {
        attackAttachment.reset(nullptr);
        holdAttachment.reset(nullptr);
        decayAttachment.reset(nullptr);
        sustainAttachment.reset(nullptr);
        releaseAttachment.reset(nullptr);
    }
    
    // 重建参数连接
    void rebuildAttachments()
    {
        // 确保之前的连接已经正确释放
        if (attackAttachment != nullptr || holdAttachment != nullptr || 
            decayAttachment != nullptr || sustainAttachment != nullptr || 
            releaseAttachment != nullptr)
        {
            releaseAttachments();
        }
        
        // 创建新的连接
        try {
            auto& tree = processor.getParameterTree();
            attackAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "attack", attackSlider);
            holdAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "hold", holdSlider);
            decayAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "decay", decaySlider);
            sustainAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "sustain", sustainSlider);
            releaseAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "release", releaseSlider);
        }
        catch (const std::exception& e) {
            // 安全处理异常
            DBG("Error rebuilding envelope attachments: " + juce::String(e.what()));
        }
    }
    
private:
    // 设置控件
    void setupControls()
    {
        // 为所有滑块设置通用样式 - 简化版，适合更小的UI
        auto setupSlider = [this](juce::Slider& slider, const juce::String& labelText) {
            slider.setSliderStyle(juce::Slider::LinearBar);
            slider.setTextBoxStyle(juce::Slider::TextBoxRight, false, 40, 15);
            slider.setTextValueSuffix(" " + labelText);
            addAndMakeVisible(slider);
        };
        
        // 设置各个参数滑块
        setupSlider(attackSlider, "A");
        setupSlider(holdSlider, "H");
        setupSlider(decaySlider, "D");
        setupSlider(sustainSlider, "S");
        setupSlider(releaseSlider, "R");
        
        // 创建参数附件
        auto& tree = processor.getParameterTree();
        attackAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "attack", attackSlider);
        holdAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "hold", holdSlider);
        decayAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "decay", decaySlider);
        sustainAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "sustain", sustainSlider);
        releaseAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(tree, "release", releaseSlider);
    }
    
    // 定时器回调函数，用于更新包络显示
    void timerCallback() override
    {
        // 重绘界面以更新包络显示
        repaint();
    }
    
    // 绘制包络曲线
    void drawEnvelopeCurve(juce::Graphics& g, const juce::Rectangle<int>& bounds)
    {
        const int width = bounds.getWidth();
        const int height = bounds.getHeight();
        
        // 获取参数值 - 直接从滑块获取值
        float attack = attackSlider.getValue();
        float hold = holdSlider.getValue();
        float decay = decaySlider.getValue();
        float sustain = sustainSlider.getValue();
        float release = releaseSlider.getValue();
        
        // 确保最小时间为0.001秒，避免除零错误
        attack = juce::jmax(0.001f, attack);
        hold = juce::jmax(0.0f, hold);
        decay = juce::jmax(0.001f, decay);
        release = juce::jmax(0.001f, release);
        
        // 总时间用于归一化
        const float totalTime = attack + hold + decay + release;
        const float normalizedAttack = attack / totalTime;
        const float normalizedHold = hold / totalTime;
        const float normalizedDecay = decay / totalTime;
        const float normalizedRelease = release / totalTime;
        
        // 各段的水平宽度（以像素为单位）
        const int attackWidth = static_cast<int>(normalizedAttack * width * 0.7f); // 为了可视化效果，缩放总宽度
        const int holdWidth = static_cast<int>(normalizedHold * width * 0.7f);
        const int decayWidth = static_cast<int>(normalizedDecay * width * 0.7f);
        const int sustainWidth = static_cast<int>(width * 0.1f); // 固定宽度的Sustain部分
        const int releaseWidth = static_cast<int>(normalizedRelease * width * 0.7f);
        
        // 计算起点
        int x = bounds.getX() + 2;
        int y = bounds.getBottom() - 2;
        
        // 创建路径
        juce::Path path;
        path.startNewSubPath(x, y); // 起点（左下角）
        
        // 绘制Attack曲线（线性上升）
        x += attackWidth;
        path.lineTo(x, bounds.getY() + 2); // Attack结束点（顶部）
        
        // 绘制Hold线（水平线）
        x += holdWidth;
        path.lineTo(x, bounds.getY() + 2);
        
        // 绘制Decay曲线（指数下降到Sustain）
        x += decayWidth;
        const int sustainY = bounds.getY() + 2 + static_cast<int>((1.0f - sustain) * (height - 4));
        path.lineTo(x, sustainY);
        
        // 绘制Sustain线（水平线）
        x += sustainWidth;
        path.lineTo(x, sustainY);
        
        // 绘制Release曲线（指数下降）
        x += releaseWidth;
        path.lineTo(x, y); // 回到底部
        
        // 设置路径样式
        g.setColour(juce::Colours::orange);
        g.strokePath(path, juce::PathStrokeType(1.5f));
    }
    
    // 处理器引用
    EnvelopeGeneratorProcessor& processor;
    
    // 参数滑块
    juce::Slider attackSlider;
    juce::Slider holdSlider;
    juce::Slider decaySlider;
    juce::Slider sustainSlider;
    juce::Slider releaseSlider;
    
    // 参数附件
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> attackAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> holdAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> decayAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> sustainAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> releaseAttachment;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(EnvelopeGeneratorProcessorComponent)
};