#include "AudioInputProcessor.h"

void AudioInputProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    // Initialize audio processing parameters
    juce::ignoreUnused(sampleRate, samplesPerBlock);
}

void AudioInputProcessor::releaseResources()
{
    // Release resources
}

void AudioInputProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    // Process audio input
    const int numInputChannels = buffer.getNumChannels();
    const int numSamples = buffer.getNumSamples();

    // Apply input gain
    for (int channel = 0; channel < numInputChannels; ++channel)
    {
        auto* channelData = buffer.getWritePointer(channel);
        for (int sample = 0; sample < numSamples; ++sample)
        {
            channelData[sample] *= inputGain;
        }
    }
    
    juce::ignoreUnused(midiMessages);
}

void AudioInputProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    // Save processor state
    auto stateTree = juce::ValueTree("AudioInputProcessorState");
    stateTree.setProperty("inputGain", inputGain, nullptr);
    
    juce::MemoryOutputStream stream(destData, true);
    stateTree.writeToStream(stream);
}

void AudioInputProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    // Restore processor state
    auto stateTree = juce::ValueTree::readFromData(data, static_cast<size_t>(sizeInBytes));
    
    if (stateTree.isValid() && stateTree.hasType("AudioInputProcessorState"))
    {
        inputGain = static_cast<float>(stateTree.getProperty("inputGain", 1.0f));
    }
} 