#pragma once

#include <JuceHeader.h>
#include <memory>
#include <juce_dsp/juce_dsp.h>

class DelayProcessor : public juce::AudioProcessor,
                      public juce::AudioProcessorValueTreeState::Listener
{
public:
    DelayProcessor();
    ~DelayProcessor() noexcept override;

    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>&, juce::MidiBuffer&) override;

    juce::AudioProcessorEditor* createEditor() override { return nullptr; }
    bool hasEditor() const override { return false; }

    const juce::String getName() const override { return "Delay"; }

    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return false; }
    bool isMidiEffect() const override { return false; }
    double getTailLengthSeconds() const override { return 0.0; }

    int getNumPrograms() override { return 1; }
    int getCurrentProgram() override { return 0; }
    void setCurrentProgram(int) override {}
    const juce::String getProgramName(int) override { return {}; }
    void changeProgramName(int, const juce::String&) override {}

    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

    bool isBusesLayoutSupported(const BusesLayout& layouts) const override
    {
        // Support stereo input/output
        if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo()
            || layouts.getMainInputChannelSet() != juce::AudioChannelSet::stereo())
            return false;

        return true;
    }

    // Parameter access
    juce::AudioProcessorValueTreeState& getParameterTree() { return *parameters; }
    void parameterChanged(const juce::String& parameterID, float newValue) override;

private:
    // Parameter management
    std::unique_ptr<juce::AudioProcessorValueTreeState> parameters;
    static juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout();

    // DelayLine related
    juce::dsp::DelayLine<float, juce::dsp::DelayLineInterpolationTypes::Linear> delayLine;
    double sampleRate;
    
    // Parameters - using SmoothedValue instead of atomic parameters
    juce::SmoothedValue<float> delayTimeSmoothed;
    juce::SmoothedValue<float> feedbackSmoothed;
    juce::SmoothedValue<float> mixSmoothed;
    
    // Current target values (used when parameters change)
    std::atomic<float> delayTime;
    std::atomic<float> feedback;
    std::atomic<float> mix;

    // Smoothing time (ms) - time for parameters to transition smoothly
    const float smoothingTimeMs = 20.0f;  

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(DelayProcessor)
};