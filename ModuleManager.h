#pragma once

#include <JuceHeader.h>
#include <map>
#include <functional>
#include <memory>
#include "ModuleSystemComponent.h"
#include "AudioGraphSnapshot.h"
#include "ModulePresetManager.h"

// Forward declarations
class juce::AudioProcessor;
class juce::Component;
namespace juce { class AudioDeviceManager; }
class ModuleUIManager;
class ModuleConnectionManager;

// Type alias for component factory function
using ComponentFactory = std::function<std::unique_ptr<juce::Component>(juce::AudioProcessor*)>;

// Module types and information namespace
JUCE_BEGIN_IGNORE_WARNINGS_MSVC(4100 4244 4996)

namespace SynthModules
{
    enum class ModuleType
    {
        Oscillator,
        TestOscillator,
        AudioInput,
        AudioOutput,
        MidiInput,
        MidiOutput,
        TestMidiOutput,
        Delay,
        EnvelopeGenerator,
        Mixer
    };

    struct ModuleInfo
    {
        ModuleType type;
        juce::String className;
        juce::String shortName;
        juce::String groupName;
    };

    extern const ModuleInfo moduleInfoTable[];

    // Module collection class
    class ModuleCollection
    {
    public:
        ModuleCollection() = default;
        ~ModuleCollection() = default;
        
        void addModule(juce::AudioProcessor* processor);
        void removeModule(juce::AudioProcessor* processor);
        void clear();
        juce::String generateUniqueModuleName(const juce::String& baseType);
        
        const std::map<juce::String, juce::AudioProcessor*>& getModules() const { return modules; }
        
    private:
        std::map<juce::String, juce::AudioProcessor*> modules;
        std::map<juce::String, int> counters;
    };
}

JUCE_END_IGNORE_WARNINGS_MSVC

// UI module information structure - can be used by all classes
struct ModuleUI
{
    juce::Rectangle<int> bounds;
    juce::String type;
    juce::String uniqueName;
    juce::AudioProcessorGraph::NodeID nodeId;
    std::unique_ptr<juce::Component> component;
    uint8_t hardwareIndex = 0;  // Hardware module index, default is 0
};

// Helper structure for module type information
struct ModuleTypeInfo
{
    juce::String fullModuleType;
    juce::String shortType;
    juce::String moduleName;
};

// Module manager class
JUCE_BEGIN_IGNORE_WARNINGS_MSVC(4100 4244 4996)

class ModuleManager : public juce::ChangeListener
{
public:
    ModuleManager(juce::AudioDeviceManager& deviceManager);
    ~ModuleManager();
    
    // Get module system
    ModuleSystemComponent* getModuleSystem() const { return moduleSystem.get(); }
    
    // Audio processing
    void prepareToPlay(double sampleRate, int samplesPerBlock);
    void processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages);
    void releaseResources();
    
    // 获取模块系统组件 - 添加此方法以便MainComponent和其他组件可以访问
    ModuleSystemComponent* getModuleSystem() const { return moduleSystem.get(); }
    
    // Create module
    bool createModuleFromName(const juce::String& command);
    
    // Module management
    juce::AudioProcessor* findModuleByName(const juce::String& name);
    
    // UI information
    const std::vector<std::unique_ptr<ModuleUI>>& getModuleUIs() const { return moduleUIs; }
    
    // ModuleUI helper methods
    juce::String getModuleNameForNodeId(juce::AudioProcessorGraph::NodeID nodeId) const;
    juce::AudioProcessorGraph::NodeID findNodeIdForName(const juce::String& moduleName) const;
    
    // Snapshot management
    void takeAudioGraphSnapshot(AudioGraphSnapshot& snapshot);
    void restoreFromAudioGraphSnapshot(const AudioGraphSnapshot& snapshot);
    
    // State management
    juce::String getModuleState() const;
    
    // Get current audio settings
    double getCurrentSampleRate() const { return currentSampleRate; }
    int getCurrentBlockSize() const { return currentBlockSize; }

    // Module existence check
    bool moduleExists(const juce::String& moduleName) const;
    
    // Audio device configuration
    void setCurrentAudioDevice(juce::AudioIODevice* device);
    
    // Change listener callback
    void changeListenerCallback(juce::ChangeBroadcaster* source) override;

    // Get processor graph
    juce::AudioProcessorGraph* getAudioGraph() { return audioGraph.get(); }
    
    // UI Layout method - for use by ModuleSystemComponent
    void layoutModules(juce::Component* container);
    
    // Friend classes for access to internal members
    friend class ModuleUIManager;
    friend class ModuleConnectionManager;

    // New method: Handle audio settings changes with module state preservation
    void handleAudioSettingsChange(juce::AudioIODevice* newDevice);

    // Method declaration for creating test arrangement
    bool createTestArrangement();
    bool createTestArrangementWithMixer();
    
    // New methods for preset-based module management
    bool createModulesFromPreset(const juce::String& presetName);
    bool createModuleArrangement(const juce::Array<juce::String>& moduleTypes, 
                                 const juce::Array<juce::var>& connections);
    
    // Method to save current setup as a preset
    bool saveCurrentSetupAsPreset(const juce::String& presetName, const juce::String& description = "");
    
    // Get the preset manager
    ModulePresetManager* getPresetManager() { return presetManager.get(); }

private:
    // Module system component
    std::unique_ptr<ModuleSystemComponent> moduleSystem;
    
    // UI module information
    std::vector<std::unique_ptr<ModuleUI>> moduleUIs;
    
    // Device manager reference
    juce::AudioDeviceManager& deviceManager;
    
    // Audio processing parameters
    double currentSampleRate = 0.0;
    int currentBlockSize = 0;
    
    // Module collection
    SynthModules::ModuleCollection moduleCollection;
    
    // UI Manager
    std::unique_ptr<ModuleUIManager> uiManager;
    
    // Connection Manager
    std::unique_ptr<ModuleConnectionManager> connectionManager;
    
    // Component factory mapping
    std::map<juce::String, ComponentFactory> componentFactories;
    
    // User interface container
    juce::Component* moduleUIsContainer = nullptr;
    
    // Module layout dimensions
    int margin = 15;
    
    std::unique_ptr<juce::AudioProcessorGraph> audioGraph;
    
    // Preset manager
    std::unique_ptr<ModulePresetManager> presetManager;
    
    // Helper methods for module creation
    ModuleTypeInfo parseModuleCommand(const juce::String& command);
    std::unique_ptr<juce::AudioProcessor> createProcessor(const juce::String& fullModuleType);
    juce::String generateModuleName(juce::AudioProcessor* processor, const ModuleTypeInfo& typeInfo);
    juce::AudioProcessorGraph::NodeID addProcessorToSystem(juce::AudioProcessor* processor);
    
    // Helper method to create default presets
    void createDefaultPresets(const juce::File& presetFile);
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModuleManager)
};

JUCE_END_IGNORE_WARNINGS_MSVC