#pragma once

#include <JuceHeader.h>
#include <juce_dsp/juce_dsp.h>

/**
 * MixerProcessor class - Implements a 4-channel stereo mixer
 * Each channel has volume, pan, and mute controls
 */
class MixerProcessor : public juce::AudioProcessor,
                       public juce::AudioProcessorValueTreeState::Listener
{
public:
    MixerProcessor();
    ~MixerProcessor() override;

    // Basic processor functions
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages) override;
    
    // Parameter Listener callback
    void parameterChanged(const juce::String& parameterID, float newValue) override;

    // State Management
    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

    // UI related
    juce::AudioProcessorEditor* createEditor() override;
    bool hasEditor() const override;

    // Basic information
    const juce::String getName() const override { return "Mixer"; }
    double getTailLengthSeconds() const override { return 0.0; }
    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return false; }
    
    // This is used to inform this processor of what buses are requested 
    bool isBusesLayoutSupported(const BusesLayout& layouts) const override
    {
        // We need 4 stereo inputs and 1 stereo output
        if (layouts.outputBuses.size() != 1 || layouts.inputBuses.size() != 4)
            return false;
            
        // Ensure the output is stereo
        if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo())
            return false;
            
        // Check each input is stereo
        for (auto i = 0; i < layouts.inputBuses.size(); ++i)
            if (layouts.inputBuses[i] != juce::AudioChannelSet::stereo())
                return false;
                
        return true;
    }

    // Parameter management
    juce::AudioProcessorValueTreeState& getParameterTree() { return parameters; }
    
    // Level meter access functions
    float getInputLevel(int channel, int lr) const;
    float getOutputLevel(int lr) const;
    
    // Number of channels constants
    static constexpr int NUM_CHANNELS = 4;
    
    // The program name isn't relevant for this processor
    int getNumPrograms() override { return 1; }
    int getCurrentProgram() override { return 0; }
    void setCurrentProgram(int) override {}
    const juce::String getProgramName(int) override { return {}; }
    void changeProgramName(int, const juce::String&) override {}

private:
    static juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout()
    {
        std::vector<std::unique_ptr<juce::RangedAudioParameter>> params;
        
        // Master volume parameter
        params.push_back(std::make_unique<juce::AudioParameterFloat>(
            "masterVolume",      // Parameter ID
            "Master Volume",     // Parameter name
            juce::NormalisableRange<float>(0.0f, 1.0f, 0.01f), // Range
            0.8f                 // Default value
        ));
        
        // Channel parameters
        for (int i = 0; i < NUM_CHANNELS; ++i)
        {
            // Volume for each channel
            params.push_back(std::make_unique<juce::AudioParameterFloat>(
                "volume" + juce::String(i),     // Parameter ID
                "Volume " + juce::String(i + 1), // Parameter name
                juce::NormalisableRange<float>(0.0f, 1.0f, 0.01f), // Range
                0.8f               // Default value
            ));
            
            // Pan for each channel
            params.push_back(std::make_unique<juce::AudioParameterFloat>(
                "pan" + juce::String(i),         // Parameter ID
                "Pan " + juce::String(i + 1),    // Parameter name
                juce::NormalisableRange<float>(-1.0f, 1.0f, 0.01f), // Range (-1 = left, 0 = center, 1 = right)
                0.0f                // Default value (center)
            ));
            
            // Mute for each channel
            params.push_back(std::make_unique<juce::AudioParameterBool>(
                "mute" + juce::String(i),         // Parameter ID
                "Mute " + juce::String(i + 1),    // Parameter name
                false              // Default value (not muted)
            ));
        }
        
        return { params.begin(), params.end() };
    }

    // Parameter state
    juce::AudioProcessorValueTreeState parameters;
    
    // DSP processing elements
    juce::dsp::Gain<float> masterGain;
    std::array<juce::dsp::Panner<float>, NUM_CHANNELS> panners;
    std::array<juce::dsp::Gain<float>, NUM_CHANNELS> channelGains;

    // Smoothed parameters for thread-safe, glitch-free updates
    juce::LinearSmoothedValue<float> smoothedMasterVolume;
    std::array<juce::LinearSmoothedValue<float>, NUM_CHANNELS> smoothedVolumes;
    std::array<juce::LinearSmoothedValue<float>, NUM_CHANNELS> smoothedPans;
    std::array<std::atomic<bool>, NUM_CHANNELS> mutes; // Mute can remain atomic as it's a boolean flag

    // Level meters
    std::array<std::array<std::atomic<float>, 2>, NUM_CHANNELS> inputLevels;  // [channel][L/R]
    std::array<std::atomic<float>, 2> outputLevels;  // [L/R]
    
    // Level meter decay rate
    float levelDecayRate;
    
    // Temporary buffer for mixer processing
    juce::AudioBuffer<float> mixBuffer;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MixerProcessor)
};
