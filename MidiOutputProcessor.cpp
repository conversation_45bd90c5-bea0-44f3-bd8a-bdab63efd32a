﻿#include "MidiOutputProcessor.h"

void MidiOutputProcessor::prepareToPlay(double sampleRate, int samplesPerBlock)
{
    // 初始化处理器
}

void MidiOutputProcessor::releaseResources()
{
    // 释放资源
}

void MidiOutputProcessor::processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages)
{
    // 处理 MIDI 消息
    if (!midiMessages.isEmpty())
    {
        // 更新 MIDI 通道
        juce::MidiBuffer processedMessages;
        for (const auto metadata : midiMessages)
        {
            auto message = metadata.getMessage();
            if (message.getChannel() != midiChannel)
            {
                message.setChannel(midiChannel);
            }
            processedMessages.addEvent(message, metadata.samplePosition);
        }
        midiMessages.swapWith(processedMessages);
    }
}

void MidiOutputProcessor::getStateInformation(juce::MemoryBlock& destData)
{
    // 保存状态
    juce::ValueTree state("MidiOutputProcessorState");
    state.setProperty("midiChannel", midiChannel, nullptr);
    
    std::unique_ptr<juce::XmlElement> xml(state.createXml());
    copyXmlToBinary(*xml, destData);
}

void MidiOutputProcessor::setStateInformation(const void* data, int sizeInBytes)
{
    // 恢复状态
    std::unique_ptr<juce::XmlElement> xmlState(getXmlFromBinary(data, sizeInBytes));
    
    if (xmlState != nullptr)
    {
        juce::ValueTree state = juce::ValueTree::fromXml(*xmlState);
        midiChannel = state.getProperty("midiChannel", 1);
    }
} 