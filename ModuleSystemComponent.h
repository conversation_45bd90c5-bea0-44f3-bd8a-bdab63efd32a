﻿#pragma once

#include <JuceHeader.h>
#include "AudioInputProcessor.h"
#include "AudioOutputProcessor.h"
#include "MidiInputProcessor.h"
#include "MidiOutputProcessor.h"
#include "TestMidiOutProcessor.h"
#include "TestOscillatorProcessor.h"
#include "DelayProcessor.h"
#include "EnvelopeGeneratorProcessor.h"
// #include "AudioAnalyzerProcessor.h"

// Processor node structure
struct ProcessorNode
{
    ProcessorNode(juce::AudioProcessor* p, const juce::String& n)
        : processor(p), name(n) {}
    
    juce::AudioProcessor* processor;
    juce::AudioProcessorGraph::NodeID nodeId;
    juce::String name;
};

// Forward declarations
class ModuleManager;
class ModuleUIManager;

class ModuleSystemComponent : public juce::Component
{
public:
    ModuleSystemComponent(ModuleManager& mm);
    ~ModuleSystemComponent() override = default;
    
    void paint(juce::Graphics&) override {}
    void resized() override;
    
    void getInterceptsMouseClicks(bool& wasIntercepting, bool& wasWantsChildrenToIntercept) const
    {
        wasIntercepting = true;
        wasWantsChildrenToIntercept = true;
    }
    
    void setAudioDeviceManager(juce::AudioDeviceManager& manager);
    
    // Audio connection management
    bool connectProcessors(juce::AudioProcessorGraph::NodeID sourceNodeId, 
                         int sourceChannelIndex,
                         juce::AudioProcessorGraph::NodeID destNodeId, 
                         int destChannelIndex);
                         
    // MIDI connection management
    bool connectMidiProcessors(juce::AudioProcessorGraph::NodeID sourceNodeId,
                             juce::AudioProcessorGraph::NodeID destNodeId);
    bool disconnectMidiProcessors(juce::AudioProcessorGraph::NodeID sourceNodeId,
                                juce::AudioProcessorGraph::NodeID destNodeId);
    bool isMidiConnected(juce::AudioProcessorGraph::NodeID sourceNodeId,
                        juce::AudioProcessorGraph::NodeID destNodeId) const;
                         
    void disconnectProcessor(juce::AudioProcessorGraph::NodeID nodeId);
                         
    juce::AudioProcessor* getProcessorForNode(juce::AudioProcessorGraph::NodeID nodeId);
    juce::AudioProcessor* getProcessorForNode(juce::AudioProcessorGraph::NodeID nodeId) const;
    
    juce::AudioProcessorGraph::NodeID addProcessor(juce::AudioProcessor* processor);
    bool removeProcessor(juce::AudioProcessorGraph::NodeID nodeId);

    // Audio processing
    void prepareToPlay(double sampleRate, int samplesPerBlock);
    void processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages);
    void releaseResources();

    // Get processors
    const std::vector<std::unique_ptr<ProcessorNode>>& getNodes() const { return nodes; }

    // Print connection information
    void printConnections() const;
    
    // Get all current connections
    std::vector<juce::AudioProcessorGraph::Connection> getConnections() const;

    // Get audio connections
    std::vector<juce::AudioProcessorGraph::Connection> getAudioConnections() const
    {
        std::vector<juce::AudioProcessorGraph::Connection> audioConnections;
        if (audioGraph)
        {
            auto allConnections = audioGraph->getConnections();
            for (const auto& conn : allConnections)
            {
                // Audio connections have channel indices
                if (conn.source.channelIndex >= 0 && conn.destination.channelIndex >= 0)
                    audioConnections.push_back(conn);
            }
        }
        return audioConnections;
    }
    
    // Get MIDI connections
    std::vector<juce::AudioProcessorGraph::Connection> getMidiConnections() const
    {
        std::vector<juce::AudioProcessorGraph::Connection> midiConnections;
        if (audioGraph)
        {
            auto allConnections = audioGraph->getConnections();
            for (const auto& conn : allConnections)
            {
                // MIDI connections have channel index -1
                if (conn.source.channelIndex == -1 && conn.destination.channelIndex == -1)
                    midiConnections.push_back(conn);
            }
        }
        return midiConnections;
    }

    // Get processor graph
    juce::AudioProcessorGraph* getAudioGraph() { return audioGraph.get(); }

    // New method: Update audio settings while preserving modules
    void updateAudioSettings(double sampleRate, int samplesPerBlock);

private:
    std::unique_ptr<juce::AudioProcessorGraph> audioGraph;
    juce::AudioDeviceManager* deviceManager = nullptr;
    std::vector<std::unique_ptr<ProcessorNode>> nodes;
    ModuleManager& moduleManager;
    
    double currentSampleRate = 44100.0;
    int currentBlockSize = 512;

    // Helper functions
    void createProcessors();
    void prepareAll();
    void connectProcessors();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(ModuleSystemComponent)
}; 