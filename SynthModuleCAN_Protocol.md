# 合成器模块CAN总线通信协议

## 1. 概述

本文档定义了香橙派5max与硬件合成器模块之间通过CAN总线进行通信的协议。此协议支持模块发现、参数初始化和实时参数控制等功能。

## 2. 物理层

- 接口：标准CAN接口（CAN 2.0A/B）
- 波特率：500 kbit/s
- 接口名称：默认为"can0"

## 3. 数据帧格式

每个CAN消息使用标准数据帧，包含8字节数据：

| 字节位置 | 名称 | 描述 |
|---------|------|------|
| 0 | moduleType | 模块类型编号（对应SynthModules::ModuleType枚举） |
| 1 | moduleIndex | 该类型模块的索引（从0开始） |
| 2 | paramIndex | 参数索引（0-255）|
| 3 | paramValue | 参数值（0-127）|
| 4-7 | additionalData | 扩展数据（预留） |

## 4. 模块类型编码

模块类型编码基于SynthModules::ModuleType枚举：

| 编码 | 模块类型 | 全名 | 简称 |
|-----|---------|------|------|
| 0x00 | Oscillator | "oscillator" | "osc" |
| 0x01 | TestOscillator | "testosc" | "tosc" |
| 0x02 | AudioInput | "input" | "ain" |
| 0x03 | AudioOutput | "output" | "aou" |
| 0x04 | MidiInput | "midiin" | "min" |
| 0x05 | MidiOutput | "midiout" | "mou" |
| 0x06 | TestMidiOutput | "testmidiout" | "tmou" |
| 0x07 | Delay | "delay" | "dey" |
| 0x08 | EnvelopeGenerator | "envelopegenerator" | "envgen" |

## 5. 通信命令

### 5.1 模块发现与连接

当硬件模块插入系统时，它会发送一个连接请求：

```
moduleType: <模块类型编码>
moduleIndex: 0（初始值，将被系统重新分配）
paramIndex: 0
paramValue: 0
```

示例：振荡器模块连接
```
[00, 00, 00, 00, 00, 00, 00, 00]
```

### 5.2 模块初始化

当系统接收到连接请求后，会创建相应的软件模块，并发送初始化消息：

```
moduleType: <模块类型编码>
moduleIndex: <分配的索引>
paramIndex: <参数编号，0-2>
paramValue: <初始参数值>
```

系统会顺序发送该模块的所有参数初始值。

示例：初始化振荡器模块（索引0）的三个参数
```
[00, 00, 00, 63] // 频率参数初始化
[00, 00, 01, 00] // 波形参数初始化
[00, 00, 02, 64] // 增益参数初始化
```

### 5.3 参数更新

当硬件模块上的控制器被调整时，它会发送参数更新消息：

```
moduleType: <模块类型编码>
moduleIndex: <模块索引>
paramIndex: <被调整的参数索引>
paramValue: <新的参数值>
```

示例：更新振荡器模块（索引0）的频率参数
```
[00, 00, 00, 75]
```

## 6. 模块参数定义

### 6.1 振荡器 (Oscillator)

| 参数索引 | 参数名称 | 范围 | APVTS参数ID |
|---------|---------|------|------------|
| 0 | 频率 | 0-127（归一化到20Hz-20kHz） | "frequency" |
| 1 | 波形 | 0=Sine, 1=Square, 2=Saw | "waveform" |
| 2 | 增益 | 0-127（归一化到0.0-1.0） | "gain" |

### 6.2 延迟 (Delay)

| 参数索引 | 参数名称 | 范围 | APVTS参数ID |
|---------|---------|------|------------|
| 0 | 延迟时间 | 0-127（归一化） | "delayTime" |
| 1 | 反馈 | 0-127（归一化到0.0-1.0） | "feedback" |
| 2 | 混合比例 | 0-127（归一化到0.0-1.0） | "mix" |

### 6.3 包络发生器 (EnvelopeGenerator)

| 参数索引 | 参数名称 | 范围 | APVTS参数ID |
|---------|---------|------|------------|
| 0 | 起音时间 | 0-127（归一化到0.001s-5.0s） | "attack" |
| 1 | 保持时间 | 0-127（归一化到0.0s-5.0s） | "hold" |
| 2 | 衰减时间 | 0-127（归一化到0.001s-5.0s） | "decay" |
| 3 | 持续电平 | 0-127（归一化到0.0-1.0） | "sustain" |
| 4 | 释音时间 | 0-127（归一化到0.001s-5.0s） | "release" |

## 7. 错误处理

- 如果收到无效的模块类型，系统将忽略该消息并记录错误
- 如果收到无效的参数索引，系统将忽略该消息并记录错误
- 如果无法找到指定的模块索引，系统将忽略该消息并记录错误

## 8. 实现注意事项

- 所有参数值使用0-127范围传输，系统内部会转换为归一化值（0.0-1.0）
- 参数更新消息会触发AudioProcessorValueTreeState中对应参数的更新
- 系统会保持模块类型到实例的映射关系，以便正确路由消息 