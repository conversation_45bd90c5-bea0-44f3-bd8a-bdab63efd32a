﻿#pragma once

#include <JuceHeader.h>

// Inherit from Listener
class AudioOutputProcessor : public juce::AudioProcessorGraph::AudioGraphIOProcessor,
                           public juce::AudioProcessorValueTreeState::Listener 
{
public:
    explicit AudioOutputProcessor(juce::AudioDeviceManager& deviceManager);

    // Destructor needs listener removal, move to cpp
    ~AudioOutputProcessor() override;

    // Basic processor functions
    void prepareToPlay(double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;
    void processBlock(juce::AudioBuffer<float>& buffer, juce::MidiBuffer& midiMessages) override;
    
    // Parameter Listener callback
    void parameterChanged(const juce::String& parameterID, float newValue) override;

    // State Management
    void getStateInformation(juce::MemoryBlock& destData) override;
    void setStateInformation(const void* data, int sizeInBytes) override;

    // UI related
    juce::AudioProcessorEditor* createEditor() override { return nullptr; }
    bool hasEditor() const override { return false; }

    // Basic information
    const juce::String getName() const override { return "Audio Output"; }
    bool acceptsMidi() const override { return false; }
    bool producesMidi() const override { return false; }

    // Parameter management
    juce::AudioProcessorValueTreeState& getParameterTree() { return parameters; }

private:
    static juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout()
    {
        std::vector<std::unique_ptr<juce::RangedAudioParameter>> params;
        params.push_back(std::make_unique<juce::AudioParameterFloat>(
            "masterVolume",     // Parameter ID
            "Master Volume",    // Parameter name
            juce::NormalisableRange<float>(0.0f, 1.0f, 0.01f), // Range
            1.0f               // Default value
        ));
        return { params.begin(), params.end() };
    }

    juce::AudioDeviceManager& deviceManager;
    juce::AudioProcessorValueTreeState parameters;

    // Atomic member to cache the parameter value
    std::atomic<float> masterVolume;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AudioOutputProcessor)
}; 