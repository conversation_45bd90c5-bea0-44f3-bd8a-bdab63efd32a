#pragma once

#include <JuceHeader.h>
#include "TestMidiOutProcessor.h"

class TestMidiOutProcessorComponent : public juce::Component
{
public:
    TestMidiOutProcessorComponent(TestMidiOutProcessor& p)
        : processor(p)
    {
        setSize(200, 120);
    }

    void paint(juce::Graphics& g) override
    {
        // Fill background
        g.fillAll(juce::Colours::darkgrey);
        
        // Draw border
        g.setColour(juce::Colours::white);
        g.drawRoundedRectangle(getLocalBounds().toFloat().reduced(2), 5.0f, 2.0f);
        
        // Draw title
        g.setFont(15.0f);
        g.drawText("Test MIDI Out", getLocalBounds().removeFromTop(30),
                  juce::Justification::centred, true);
    }

    void resized() override
    {
        // Simple UI, no additional controls needed
    }

private:
    TestMidiOutProcessor& processor;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(TestMidiOutProcessorComponent)
}; 